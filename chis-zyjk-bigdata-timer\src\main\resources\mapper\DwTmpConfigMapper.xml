<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.zyjk.bigdata.timer.mapper.DwTmpConfigMapper">

    <resultMap id="BaseResultMap" type="com.chis.zyjk.bigdata.common.pojo.po.DwTmpConfigPO">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="dwLevel" column="dw_level" jdbcType="VARCHAR"/>
            <result property="tableName" column="table_name" jdbcType="VARCHAR"/>
            <result property="dwSource" column="dw_source" jdbcType="VARCHAR"/>
            <result property="cleanMode" column="clean_mode" jdbcType="VARCHAR"/>
            <result property="executeNum" column="execute_num" jdbcType="INTEGER"/>
            <result property="executeMode" column="execute_mode" jdbcType="VARCHAR"/>
            <result property="executeApi" column="execute_api" jdbcType="VARCHAR"/>
            <result property="executeSql" column="execute_sql" jdbcType="VARCHAR"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="executeState" column="execute_state" jdbcType="INTEGER"/>
            <result property="executeParam" column="execute_param" jdbcType="VARCHAR"/>
            <result property="ifEnable" column="if_enable" jdbcType="INTEGER"/>
            <result property="revision" column="revision" jdbcType="INTEGER"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,dw_level,table_name,
        dw_source,clean_mode,execute_num,
        execute_mode,execute_api,execute_sql,
        start_time,end_time,execute_state,
        execute_param,if_enable,revision,
        del_flag,create_time,create_by,
        update_time,update_by
    </sql>
</mapper>
