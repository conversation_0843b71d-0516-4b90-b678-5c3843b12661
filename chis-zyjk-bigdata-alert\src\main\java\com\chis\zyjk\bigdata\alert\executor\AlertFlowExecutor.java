package com.chis.zyjk.bigdata.alert.executor;

import com.chis.project.frame.common.tools.core.util.ObjectUtil;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.zyjk.bigdata.alert.liteflow.common.ContextHelper;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowErrorCode;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowExceptionHelper;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowRuleException;
import com.chis.zyjk.bigdata.alert.liteflow.pojo.context.CmpContext;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertGlobalContext;
import com.chis.zyjk.core.common.enums.ZyjkResultEnum;
import com.way.common.core.exception.ServiceException;
import com.yomahub.liteflow.builder.el.LiteFlowChainELBuilder;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import com.yomahub.liteflow.slot.DefaultContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * LiteFlow规则执行器
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlertFlowExecutor {

    @Resource
    private FlowExecutor flowExecutor;

    /**
     * 执行LiteFlow规则（纯动态配置模式）
     *
     * @param globalContext 预警全局上下文
     * @param cmpConfigMap  预警组件配置
     */
    public LiteflowResponse executeRule(AlertGlobalContext globalContext, Map<String, JSONObject> cmpConfigMap) {
        log.info("开始执行预警{}", globalContext.toString());

        try {
            // 检查配置信息
            checkAlertContext(globalContext, cmpConfigMap);

            // 初始化组件上下文
            CmpContext allCmpContext = new CmpContext();
            for (Map.Entry<String, JSONObject> entry : cmpConfigMap.entrySet()) {
                JSONObject cmpContext = new JSONObject();
                cmpContext.set(ContextHelper.CONFIG_CONTEXT_KEY, entry.getValue());
                cmpContext.set(ContextHelper.DATA_CONTEXT_KEY, new JSONObject());
                allCmpContext.setData(entry.getKey(), cmpContext);
            }

            // 动态创建并执行LiteFlow表达式
            String chainId = "DYNAMIC_CHAIN_" + System.currentTimeMillis();
            // 动态创建chain
            LiteFlowChainELBuilder.createChain()
                    .setChainId(chainId)
                    .setEL(globalContext.getExpression())
                    .build();
            // 执行chain
            return flowExecutor.execute2Resp(chainId, null, globalContext, allCmpContext);
        } catch (LiteFlowRuleException e) {
            throw new ServiceException(e.toString(), ZyjkResultEnum.INTERNAL_SERVER_ERROR.getCode());
        } catch (Exception e) {
            LiteFlowRuleException ruleException = LiteFlowExceptionHelper.createRuleException(
                    e,
                    LiteFlowErrorCode.RULE_EXECUTION_FAILED,
                    globalContext.getRuleCode()
            );
            throw new ServiceException(ruleException.toString(), ZyjkResultEnum.INTERNAL_SERVER_ERROR.getCode());
        }
    }

    /**
     * 验证上下文
     *
     * @param globalContext 预警全局上下文
     * @param cmpConfigMap  预警组件配置
     */
    public void checkAlertContext(AlertGlobalContext globalContext, Map<String, JSONObject> cmpConfigMap) {
        if (ObjectUtil.isEmpty(globalContext)) {
            throw LiteFlowExceptionHelper.createRuleException(
                    LiteFlowErrorCode.RULE_EMPTY
            );
        }
        if (ObjectUtil.isEmpty(globalContext.getExpression())) {
            throw LiteFlowExceptionHelper.createRuleException(
                    LiteFlowErrorCode.RULE_EXPRESSION_EMPTY
            );
        }
        if (ObjectUtil.isEmpty(globalContext.getRuleCode())) {
            throw LiteFlowExceptionHelper.createRuleException(
                    LiteFlowErrorCode.RULE_VALIDATION_FAILED,
                    LiteFlowErrorCode.RULE_VALIDATION_FAILED.getDesc() + ": 规则编码为空",
                    ""
            );
        }
        if (ObjectUtil.isEmpty(globalContext.getDeDupKeyExpression())) {
            throw LiteFlowExceptionHelper.createRuleException(
                    LiteFlowErrorCode.RULE_VALIDATION_FAILED,
                    LiteFlowErrorCode.RULE_VALIDATION_FAILED.getDesc() + ": 重复去重表达式为空",
                    globalContext.getRuleCode()
            );
        }
        if (ObjectUtil.isEmpty(cmpConfigMap)) {
            throw LiteFlowExceptionHelper.createRuleException(
                    LiteFlowErrorCode.RULE_CMP_CONFIG_EMPTY
            );
        }
    }

    /**
     * 初始化执行上下文
     *
     * @param ruleCode 规则编码
     * @return 执行上下文
     */
    private DefaultContext initializeContext(String ruleCode) {
        return new DefaultContext();
    }

    /**
     * 处理执行结果
     *
     * @param response LiteFlow响应=
     */
    private void processExecutionResult(LiteflowResponse response) {
    }


}
