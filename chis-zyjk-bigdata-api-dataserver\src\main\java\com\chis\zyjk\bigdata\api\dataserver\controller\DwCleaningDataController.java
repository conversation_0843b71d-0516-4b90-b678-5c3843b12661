package com.chis.zyjk.bigdata.api.dataserver.controller;


import com.chis.zyjk.bigdata.api.dataserver.service.DwCleaningDataService;
import com.chis.zyjk.bigdata.common.pojo.dto.dataclean.DataCleaningDTO;
import com.chis.zyjk.bigdata.common.pojo.vo.dataclean.DataCleaningVO;
import com.way.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数仓分层数据清洗定时任务
 */
@Api(tags = {"数仓清洗管理"})
@Slf4j
@RestController
@RequestMapping("/dwCleaning")
@RequiredArgsConstructor
public class DwCleaningDataController {

    private final DwCleaningDataService dwCleaningDataService;

    @ApiOperation("数据清洗工具")
    @PostMapping("/dataCleaning")
    public R<DataCleaningVO> dataCleaning(@RequestBody @Validated DataCleaningDTO dataCleaningDTO) {
        DataCleaningVO dataCleaningVO = dwCleaningDataService.cleanTableData(dataCleaningDTO);
        return R.ok(dataCleaningVO);
    }


}
