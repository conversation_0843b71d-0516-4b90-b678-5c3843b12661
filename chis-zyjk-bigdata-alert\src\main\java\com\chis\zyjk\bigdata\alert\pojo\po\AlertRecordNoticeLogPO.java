package com.chis.zyjk.bigdata.alert.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.zyjk.core.common.pojo.ZyjkPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预警记录通知子表
 */
@Data
@TableName("alert_record_notice_log")
@EqualsAndHashCode(callSuper = false)
public class AlertRecordNoticeLogPO extends ZyjkPO {

    /**
     * 关联alert_record.id
     */
    private String alertRecordId;

    /**
     * 关联alert_record_log.id
     */
    private String alertRecordLogId;

    /**
     * 冗余字段，alert_record_log_id不为空有值，预警规则编码，关联alert_rule.rule_code
     */
    private String ruleCode;

    /**
     * 冗余字段，alert_record_log_id不为空有值，去重键值
     */
    private String deDupValue;

    /**
     * 冗余字段，alert_record_log_id不为空有值，变更类型：CREATE/UPDATE/DISPOSE
     */
    private String changeType;

    /**
     * 冗余字段，alert_record_log_id不为空有值，预警级别编码
     */
    private String alertLevel;

    /**
     * 冗余字段，alert_record_log_id不为空有值，预警值
     */
    private String alertValue;

    /**
     * 冗余字段，alert_record_log_id不为空有值，状态：0未处置 1已处置 2已忽略
     */
    private Integer status;

    /**
     * 冗余字段，alert_record_log_id不为空有值，预警内容
     */
    private String alertContent;

    /**
     * 冗余字段，alert_record_log_id不为空有值，通知自定义JSON
     */
    private String alertJson;

    /**
     * 冗余字段，alert_record_log_id不为空有值，预警源数据JSON
     */
    private String sourceData;

    /**
     * 通知内容
     */
    private String noticeContent;

    /**
     * 通知结果
     */
    private String noticeResult;
}
