package com.chis.zyjk.bigdata.api.dataserver.mapper;

import com.chis.project.frame.common.tools.core.date.DateUtil;
import com.chis.project.frame.common.tools.core.util.ObjectUtil;
import com.way.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Doris数据库访问层
 */
@Slf4j
@Repository
public class DwDorisDao {

    @Qualifier("dorisJdbcTemplate")
    private final JdbcTemplate jdbcTemplate;

    public DwDorisDao(@Qualifier("dorisJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public void cleanBusinessData(String sql, LocalDateTime lastTime) {
        NamedParameterJdbcTemplate namedTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
        int lastTimeIndex = sql.indexOf(":lastTime");
        Map<String, Object> paramMap = new HashMap<>();
        // 时间挫标识
        if (lastTimeIndex >= 0) {
            if (ObjectUtil.isEmpty(lastTime)) {
                lastTime = DateUtil.parseLocalDateTime("1999-01-01 00:00:00");
            }
            // 查询目标表最大时间挫
            paramMap.put("lastTime", lastTime);
        }

        int updateNum = 0;
        try {
            updateNum = namedTemplate.update(sql, paramMap);
        } catch (Exception e) {
            throw new ServiceException("执行SQL异常: " + e.getMessage());
        }
        // 记录SQL日志，便于调试
        log.info("执行数据清洗SQL: {}, 参数: {},执行条数: {}", sql, paramMap, updateNum);
    }


    public LocalDateTime targetTableNameMaxTime(String tableName) {
        try {
            // 获取目标表最大时间戳
            String sql = "SELECT MAX(SYS_TIME_STAMP_DORIS) FROM " + tableName;
            return jdbcTemplate.queryForObject(sql, LocalDateTime.class);
        } catch (Exception e) {
            log.error("查询表{}最大时间戳失败", tableName, e);
            throw new ServiceException("查询表最大时间戳失败");
        }
    }

    /**
     * 查询表是否存在
     *
     * @param tableName 表名
     * @return 是否存在
     */
    public boolean tableExists(String tableName) {
        String sql = "SHOW TABLES LIKE ?";
        List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, tableName);
        return !result.isEmpty();
    }

}
