package com.chis.zyjk.bigdata.alert.util;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.function.Function;

/**
 * 通用批量处理工具类
 * 提供通用的批量操作方法，支持各种批量操作
 */
@Slf4j
public class BatchProcessHelper {

    /**
     * 默认批次大小
     */
    public static final int DEFAULT_BATCH_SIZE = 100;

    /**
     * 通用批量处理方法
     *
     * @param dataList      要处理的数据列表
     * @param batchSize     批次大小
     * @param processor     批次处理器，返回处理的记录数
     * @param operationName 操作名称，用于日志
     * @param <T>           数据类型
     * @return 是否成功
     */
    public static <T> boolean processBatch(List<T> dataList,
                                           int batchSize,
                                           Function<List<T>, Integer> processor,
                                           String operationName) {
        if (dataList == null || dataList.isEmpty()) {
            log.debug("{}：数据列表为空，跳过处理", operationName);
            return true;
        }

        int totalSize = dataList.size();
        int totalProcessed = 0;

        log.info("开始{}，总数: {}, 批次大小: {}", operationName, totalSize, batchSize);

        try {
            // 分批处理
            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                List<T> batch = dataList.subList(i, endIndex);

                log.debug("处理批次 {}-{}/{}", i + 1, endIndex, totalSize);

                // 执行批次处理
                Integer processedCount = processor.apply(batch);
                if (processedCount != null) {
                    totalProcessed += processedCount;
                }

                log.debug("批次处理完成，处理记录数: {}", processedCount);
            }

            log.info("{}完成，总数: {}, 实际处理: {}", operationName, totalSize, totalProcessed);
            return true;

        } catch (Exception e) {
            log.error("{}失败，总数: {}, 已处理: {}, 错误: {}",
                    operationName, totalSize, totalProcessed, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 使用默认批次大小的批量处理方法
     *
     * @param dataList      要处理的数据列表
     * @param processor     批次处理器
     * @param operationName 操作名称
     * @param <T>           数据类型
     * @return 是否成功
     */
    public static <T> boolean processBatch(List<T> dataList,
                                           Function<List<T>, Integer> processor,
                                           String operationName) {
        return processBatch(dataList, DEFAULT_BATCH_SIZE, processor, operationName);
    }

    /**
     * 简化版批量处理方法（不关心返回的处理记录数）
     *
     * @param dataList      要处理的数据列表
     * @param batchSize     批次大小
     * @param processor     批次处理器（无返回值）
     * @param operationName 操作名称
     * @param <T>           数据类型
     * @return 是否成功
     */
    public static <T> boolean processBatchSimple(List<T> dataList,
                                                 int batchSize,
                                                 java.util.function.Consumer<List<T>> processor,
                                                 String operationName) {
        return processBatch(dataList, batchSize, batch -> {
            processor.accept(batch);
            return batch.size();
        }, operationName);
    }

    /**
     * 使用默认批次大小的简化版批量处理方法
     *
     * @param dataList      要处理的数据列表
     * @param processor     批次处理器（无返回值）
     * @param operationName 操作名称
     * @param <T>           数据类型
     * @return 是否成功
     */
    public static <T> boolean processBatchSimple(List<T> dataList,
                                                 java.util.function.Consumer<List<T>> processor,
                                                 String operationName) {
        return processBatchSimple(dataList, DEFAULT_BATCH_SIZE, processor, operationName);
    }
}
