package com.chis.zyjk.bigdata.common.pojo.dto.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(description = "根据参数键名查询参数值入参")
@AllArgsConstructor
public class ConfigKeyDTO {
    @ApiModelProperty(value = "参数键", required = true)
    @NotBlank(message = "参数键不能为空")
    private String configKey;
}
