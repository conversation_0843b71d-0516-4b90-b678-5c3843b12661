package com.chis.zyjk.bigdata.common.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.zyjk.core.common.pojo.ZyjkPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 数据仓库分层信息
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "dw_tmp_config")
@Data
public class DwTmpConfigPO extends ZyjkPO {

    /**
     * 分层
     */
    private String dwLevel;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 数据来源
     */
    private String dwSource;

    /**
     * 清洗模式 1: 批处理 2：物化视图 3：其他
     */
    private Integer cleanMode;

    /**
     * 执行顺序
     */
    private Integer executeNum;

    /**
     * 执行方式 1: sql 2: api
     */
    private Integer executeMode;

    /**
     * 执行接口地址
     */
    private String executeApi;

    /**
     * 执行sql
     */
    private String executeSql;

    /**
     * 执行开始时间
     */
    private LocalDateTime startTime;

    /**
     * 执行结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行状态 0 待执行 1 执行中  2 成功 3 失败'
     */
    private Integer executeState;

    /**
     * 执行参数
     */
    private String executeParam;

    /**
     * 启用状态:0：否，1：是
     */
    private Integer ifEnable;

}