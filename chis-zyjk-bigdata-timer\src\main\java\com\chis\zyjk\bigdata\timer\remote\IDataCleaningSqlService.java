package com.chis.zyjk.bigdata.timer.remote;

import com.chis.zyjk.bigdata.common.pojo.dto.dataclean.DataCleaningDTO;
import com.chis.zyjk.bigdata.common.pojo.vo.dataclean.DataCleaningVO;
import com.way.common.core.domain.R;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 数据清洗SQL服务接口
 * 具体化的服务接口，避免泛型问题
 *
 * <AUTHOR>
 * @since 2025/7/10
 */
public interface IDataCleaningSqlService {
    
    /**
     * 执行数据清洗
     *
     * @param data 清洗数据DTO
     * @return 清洗结果
     */
    @PostMapping(
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    R<DataCleaningVO> execute(@RequestBody DataCleaningDTO data);
}
