package com.chis.zyjk.bigdata.alert.liteflow.common;

import com.chis.project.frame.common.tools.core.util.StrUtil;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.yomahub.liteflow.slot.DefaultContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * LiteFlow上下文辅助工具类
 * <p>
 * 提供数据传递、上下文管理、变量替换等功能
 */
@Slf4j
@Component
public class ContextHelper {

    public static final String DATA_CONTEXT_KEY = "data";
    public static final String DATA_CONTEXT_PATH = ".data";
    public static final String CONFIG_CONTEXT_KEY = "config";
    public static final String CONFIG_CONTEXT_PATH = ".config";

    /**
     * 获取或创建上下文
     */
    public static JSONObject getOrCreateContext(DefaultContext context, String path) {
        Object dataObject = ContextHelper.getContextData(path, context);
        JSONObject contextData;
        if (dataObject instanceof JSONObject) {
            contextData = (JSONObject) dataObject;
        } else {
            contextData = new JSONObject();
            ContextHelper.setContextData(path, contextData, context);
        }
        return contextData;
    }

    /**
     * 获取或创建配置上下文
     */
    public static JSONObject getOrCreateContextConfig(DefaultContext context, String tag) {
        return getOrCreateContext(context, tag + CONFIG_CONTEXT_PATH);
    }

    /**
     * 获取或创建数据上下文
     */
    public static JSONObject getOrCreateContextData(DefaultContext context, String tag) {
        return getOrCreateContext(context, tag + DATA_CONTEXT_PATH);
    }

    /**
     * 设置上下文数据
     *
     * @param contextPath 上下文路径
     * @param value       值
     * @param context     LiteFlow执行上下文
     */
    public static void setContextData(String contextPath, Object value, DefaultContext context) {
        try {
            setNestedValueToContext(contextPath, value, context);
            log.debug("设置上下文数据，path: {}", contextPath);
        } catch (Exception e) {
            log.error("设置上下文数据失败，path: {}", contextPath, e);
            throw e;
        }
    }

    /**
     * 根据tag设置数据上下文数据
     *
     * @param tag         tag
     * @param contextPath 上下文路径
     * @param value       值
     * @param context     LiteFlow执行上下文
     */
    public static void setContextData(String tag, String contextPath, Object value, DefaultContext context) {
        setContextData(tag + DATA_CONTEXT_PATH + "." + contextPath, value, context);
    }

    /**
     * 获取上下文数据
     *
     * @param contextPath 上下文路径
     * @param context     LiteFlow执行上下文
     * @return 数据值
     */
    public static Object getContextData(String contextPath, DefaultContext context) {
        try {
            return getNestedValueFromContext(contextPath, context);
        } catch (Exception e) {
            log.error("获取上下文数据失败，path: {}", contextPath, e);
            throw e;
        }
    }

    /**
     * 从Context获取嵌套值
     *
     * @param path    路径，支持点号分隔
     * @param context LiteFlow执行上下文
     * @return 值
     */
    private static Object getNestedValueFromContext(String path, DefaultContext context) {
        if (StrUtil.isBlank(path)) {
            return null;
        }

        String[] parts = path.split("\\.");

        // 从Context获取根对象
        Object current = context.getData(parts[0]);

        // 遍历路径获取嵌套值
        for (int i = 1; i < parts.length; i++) {
            if (current == null) {
                return null;
            }

            if (current instanceof JSONObject) {
                current = ((JSONObject) current).get(parts[i]);
            } else if (current instanceof Map) {
                current = ((Map<?, ?>) current).get(parts[i]);
            } else {
                return null;
            }
        }

        return current;
    }

    /**
     * 设置嵌套值到Context
     *
     * @param path    路径，支持点号分隔
     * @param value   值
     * @param context LiteFlow执行上下文
     */
    @SuppressWarnings("unchecked")
    private static void setNestedValueToContext(String path, Object value, DefaultContext context) {
        if (StrUtil.isBlank(path)) {
            return;
        }

        String[] parts = path.split("\\.");
        if (parts.length == 1) {
            context.setData(parts[0], value);
            return;
        }

        // 获取或创建根对象
        Object data = context.getData(parts[0]);
        if (!(data instanceof Map)) {
            data = new JSONObject();
            context.setData(parts[0], data);
        }
        Map<String, Object> current = (Map<String, Object>) data;

        // 遍历路径创建嵌套结构
        for (int i = 1; i < parts.length - 1; i++) {
            String part = parts[i];
            Object next = current.get(part);

            if (!(next instanceof Map)) {
                next = new JSONObject();
                current.put(part, next);
            }

            current = (Map<String, Object>) next;
        }

        // 设置最终值
        current.put(parts[parts.length - 1], value);
    }


}
