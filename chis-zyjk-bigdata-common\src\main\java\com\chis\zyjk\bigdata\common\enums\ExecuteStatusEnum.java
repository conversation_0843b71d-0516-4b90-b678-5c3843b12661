package com.chis.zyjk.bigdata.common.enums;

import com.chis.zyjk.core.common.enums.base.NumericCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 执行转态枚举
 */
@Getter
@AllArgsConstructor
public enum ExecuteStatusEnum implements NumericCodeEnum {
    PERFORMED(0, "待执行"),
    GOING(1, "执行中"),
    SUCCESS(2, "成功"),
    FAIL(3, "失败");

    private final Integer code;
    private final String desc;

    @Override
    public String getEnumDesc() {
        return "执行转态";
    }
}
