package com.chis.zyjk.bigdata.alert.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.zyjk.core.common.pojo.ZyjkPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预警规则归档表
 */
@Data
@TableName("alert_rule_archive")
@EqualsAndHashCode(callSuper = false)
public class AlertRuleArchivePO extends ZyjkPO {

    /**
     * 预警规则编码，关联alert_rule.rule_code
     */
    private String ruleCode;

    /**
     * 归档版本号
     */
    private String archiveVersion;

    /**
     * 归档原因
     */
    private String archiveReason;

    /**
     * 完整配置快照JSON
     */
    private String snapshot;
}
