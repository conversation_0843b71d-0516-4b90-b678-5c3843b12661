package com.chis.zyjk.bigdata.common.pojo.dto.dataclean;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/7/8 16:03
 */
@Data
public class DataCleaningDTO implements Serializable {

    /**
     * 表名
     */
    @NotBlank(message = "表名不能为空")
    private String tableName;

    /**
     * 执行sql
     */
    @NotBlank(message = "执行sql")
    private String executeSql;

    /**
     * 执行参数（时间）
     */
    private String executeParam;

    /**
     * 是否加密,远程调接口executeSql需要加密，接口调用不需要
     */
    private Boolean encodeFlag = false;

}
