<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.chis.zyjk</groupId>
        <artifactId>chis-zyjk-bigdata-platform</artifactId>
        <version>1.0.0.ALPHA</version>
    </parent>

    <artifactId>chis-zyjk-bigdata-timer</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-boot.version>2.7.18</spring-boot.version>
        <druid.version>1.2.16</druid.version>
        <mysql.version>8.0.28</mysql.version>
        <fastjson.version>1.2.83</fastjson.version>
        <hutool.version>5.8.38</hutool.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.chis.zyjk</groupId>
            <artifactId>chis-zyjk-bigdata-common</artifactId>
            <version>1.0.0.ALPHA</version>
        </dependency>

        <dependency>
            <groupId>com.chis.zyjk</groupId>
            <artifactId>chis-zyjk-starter-schedulejob</artifactId>
            <version>1.0.0.ALPHA</version>
        </dependency>

        <dependency>
            <groupId>com.chis.zyjk</groupId>
            <artifactId>chis-zyjk-starter-datasync</artifactId>
            <version>1.0.0.ALPHA</version>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-jackson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chis.frame.base</groupId>
            <artifactId>chis-xxljob-spring-boot-starter</artifactId>
        </dependency>

        <!-- Druid数据源 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version> <!-- 推荐最新稳定版 -->
            <scope>provided</scope>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>