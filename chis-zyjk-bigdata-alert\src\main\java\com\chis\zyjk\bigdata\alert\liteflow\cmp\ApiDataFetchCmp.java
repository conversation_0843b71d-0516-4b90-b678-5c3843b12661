package com.chis.zyjk.bigdata.alert.liteflow.cmp;

import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.zyjk.bigdata.alert.liteflow.core.ChisNodeComponent;
import com.chis.zyjk.bigdata.alert.liteflow.pojo.context.CmpContext;
import com.chis.zyjk.bigdata.alert.liteflow.utils.BaseApiNodeUtils;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertCmpContext;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertGlobalContext;
import com.yomahub.liteflow.annotation.LiteflowComponent;

import java.util.List;
import java.util.Map;

/**
 * API数据获取组件
 * <p>
 * <b>apiDataFetch</b>
 */
@LiteflowComponent(id = "apiDataFetch", name = "API数据获取组件")
public class ApiDataFetchCmp extends ChisNodeComponent<AlertGlobalContext, AlertCmpContext> {

    @Override
    public void doProcess() {
        // 执行单次API数据获取
        List<Object> dataList = fetchSingleData(config, cmp);

        // 更新数据到上下文供其他节点使用
        BaseApiNodeUtils.updateCurrentDataToNodeHelper(tag, dataList, cmp);
    }

    @Override
    public String[] getMustConfigList() {
        return new String[]{"apiUrl"};
    }

    /**
     * 执行单次API数据获取
     */
    private List<Object> fetchSingleData(JSONObject config, CmpContext context) {
        // 获取上下文进行变量替换
        // 构建API请求参数
        Map<String, Object> params = BaseApiNodeUtils.buildApiParams(this.tag, config, contextBeanList);

        // 构建请求头
        Map<String, String> headers = BaseApiNodeUtils.buildApiHeaders(this.tag, config, contextBeanList);

        String apiUrl = config.getStr("apiUrl");
        String method = config.getStr("method", "POST");
        int timeout = config.getInt("timeout", 30000);

        logger.debug("调用API获取数据, url: {}", apiUrl);

        // 执行API调用
        String responseBody = BaseApiNodeUtils.executeApiCall(apiUrl, method, params, headers, timeout, this.getTag());

        // 保存原始响应数据
        BaseApiNodeUtils.saveResponseToContext(context, this.getTag(), responseBody);

        // 提取结果数据
        return BaseApiNodeUtils.extractResultData(responseBody, config);
    }

}
