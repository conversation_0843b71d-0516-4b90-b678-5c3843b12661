package com.chis.zyjk.bigdata.alert.liteflow.pojo.dto;

import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.zyjk.core.common.pojo.ZyjkDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "预警规则执行请求DTO")
public class AlertRuleRequestDTO extends ZyjkDTO {
    /**
     * 规则编码
     */
    @ApiModelProperty(value = "规则编码")
    @NotBlank(message = "规则编码不能为空")
    private String ruleCode;
    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    private String ruleName;
    /**
     * 规则表达式
     */
    @ApiModelProperty(value = "规则表达式")
    @NotBlank(message = "规则表达式不能为空")
    private String expression;
    /**
     * 去重表达式
     */
    @ApiModelProperty(value = "去重表达式")
    @NotBlank(message = "去重表达式不能为空")
    private String deDupKeyExpression;
    /**
     * 预警值表达式
     */
    @ApiModelProperty(value = "预警值表达式")
    private String alertValueExpression;

    /**
     * 节点配置映射
     * key: 节点tag
     * value: 节点配置JSON对象
     */
    @ApiModelProperty(value = "节点配置映射", notes = "key: 节点tag, value: 节点配置JSON对象")
    @NotEmpty(message = "节点配置映射不能为空")
    private Map<String, JSONObject> nodeConfigs;

}
