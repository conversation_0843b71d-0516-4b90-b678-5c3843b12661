<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chis.zyjk.bigdata.alert.mapper.AlertRuleArchiveMapper">

    <resultMap id="BaseResultMap" type="com.chis.zyjk.bigdata.alert.pojo.po.AlertRuleArchivePO">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="ruleCode" column="rule_code" jdbcType="VARCHAR"/>
        <result property="archiveVersion" column="archive_version" jdbcType="VARCHAR"/>
        <result property="archiveReason" column="archive_reason" jdbcType="VARCHAR"/>
        <result property="snapshot" column="snapshot" jdbcType="LONGVARCHAR"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, rule_code, archive_version, archive_reason, snapshot, revision, del_flag,
        create_time, create_by, update_time, update_by
    </sql>

</mapper>
