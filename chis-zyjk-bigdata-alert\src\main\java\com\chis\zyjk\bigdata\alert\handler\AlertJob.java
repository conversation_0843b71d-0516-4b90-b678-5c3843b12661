package com.chis.zyjk.bigdata.alert.handler;

import com.chis.project.frame.common.tools.core.util.StrUtil;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.project.frame.common.tools.json.JSONUtil;
import com.chis.zyjk.bigdata.alert.executor.AlertFlowExecutor;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertGlobalContext;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRulePO;
import com.chis.zyjk.bigdata.alert.service.AlertRuleService;
import com.chis.zyjk.core.common.enums.ZyjkResultEnum;
import com.chis.zyjk.schdulejob.ScheduleJobExecutor;
import com.chis.zyjk.schdulejob.param.JobParam;
import com.chis.zyjk.schdulejob.service.ScheduleJobService;
import com.way.common.core.exception.ServiceException;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yomahub.liteflow.flow.LiteflowResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 预警任务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlertJob extends ScheduleJobExecutor<JobParam, String> {

    private final ScheduleJobService scheduleJobService;
    private final AlertRuleService alertRuleService;
    private final AlertFlowExecutor executor;

    /**
     * 预警任务
     */
    @XxlJob("alertJobHandler")
    public void alertJobHandler() {
        try {
            start(parseAndValidateJobParam(XxlJobHelper.getJobParam()));
        } catch (Exception e) {
            log.error("预警任务异常：{}", e.getMessage(), e);
            XxlJobHelper.log("预警任务异常：{}", e.getMessage());
            XxlJobHelper.handleFail(e.getMessage());
        }
    }

    @Override
    public ScheduleJobService getScheduleJobService() {
        return scheduleJobService;
    }

    @Override
    public String getJobName() {
        return "alert_";
    }

    /**
     * 解析任务参数
     *
     * @param jobParamStr 任务参数
     * @return 任务参数
     */
    public JobParam parseAndValidateJobParam(String jobParamStr) {
        if (StrUtil.isBlank(jobParamStr)) {
            throw new ServiceException("任务参数不能为空");
        }

        JobParam param = JSONUtil.toBean(jobParamStr, JobParam.class);
        if (param == null) {
            throw new ServiceException("任务参数解析失败");
        }
        return param;
    }

    @Override
    public String execute(String fullJobName, JobParam param, String jobResult) {
        try {
            log.debug("开始执行预警任务，任务参数：{}", JSONUtil.toJsonStr(param));
            XxlJobHelper.log("开始执行预警任务，任务参数：{}", JSONUtil.toJsonStr(param));
            AlertRulePO alertRulePO = alertRuleService.getByRuleCode(param.getJobNameSuffix());
            if (alertRulePO == null) {
                log.error("任务参数有误，请检查任务参数：{}", JSONUtil.toJsonStr(param));
                XxlJobHelper.log("任务参数有误，请检查任务参数：{}", JSONUtil.toJsonStr(param));
                scheduleJobService.failure(fullJobName, "任务参数有误，请检查任务参数");
                throw new ServiceException("任务参数有误，请检查任务参数");
            }

            AlertGlobalContext globalContext = new AlertGlobalContext();
            globalContext.setRuleCode(alertRulePO.getRuleCode());
            globalContext.setRuleName(alertRulePO.getRuleName());
            globalContext.setExpression(alertRulePO.getLiteFlowConfig());
            globalContext.setDeDupKeyExpression(alertRulePO.getDeDupKeyExpression());
            globalContext.setAlertValueExpression(alertRulePO.getAlertValueExpression());
            JSONObject liteFlowNodeConfig = JSONUtil.parseObj(alertRulePO.getLiteFlowNodeConfig());
            Map<String, JSONObject> nodeConfigs = new HashMap<>();
            for (Map.Entry<String, Object> entry : liteFlowNodeConfig.entrySet()) {
                nodeConfigs.put(entry.getKey(), (JSONObject) entry.getValue());
            }
            // 执行规则
            LiteflowResponse response = executor.executeRule(globalContext, nodeConfigs);
            if (!response.isSuccess()) {
                log.error("任务执行失败：{}", response.getCause().toString());
                XxlJobHelper.log("任务执行失败：{}", response.getCause().toString());
                scheduleJobService.failure(fullJobName, "任务执行失败" + response.getCause().toString());
                throw new ServiceException(response.getCause().toString(), ZyjkResultEnum.INTERNAL_SERVER_ERROR.getCode());
            }
        } catch (Exception e) {
            log.error("数据同步任务执行异常：{}", e.getMessage(), e);
            XxlJobHelper.log("数据同步任务执行异常：{}", e.getMessage());
            scheduleJobService.failure(fullJobName, e.getMessage());
            throw new ServiceException("数据同步任务执行异常");
        }
        return "";
    }

    @Override
    public String initResult(JobParam param) {
        return "";
    }

    @Override
    public Class<String> getJobResultClazz() {
        return String.class;
    }
}
