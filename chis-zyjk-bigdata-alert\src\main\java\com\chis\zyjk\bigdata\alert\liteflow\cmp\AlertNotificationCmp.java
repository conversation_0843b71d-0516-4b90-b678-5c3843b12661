package com.chis.zyjk.bigdata.alert.liteflow.cmp;

import com.chis.project.frame.common.tools.core.collection.CollUtil;
import com.chis.project.frame.common.tools.core.convert.Convert;
import com.chis.zyjk.bigdata.alert.liteflow.common.ContextHelper;
import com.chis.zyjk.bigdata.alert.liteflow.core.ChisNodeComponent;
import com.chis.zyjk.bigdata.alert.liteflow.utils.BaseApiNodeUtils;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertCmpContext;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertGlobalContext;
import com.chis.zyjk.bigdata.alert.pojo.dto.AlertRecordDTO;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordNoticeLogPO;
import com.chis.zyjk.bigdata.alert.service.AlertRecordNoticeLogService;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 预警通知组件
 * <p>
 * 根据传入的List<AlertRecordDTO>生成AlertRecordNoticeLogPO列表，
 * 把数据通过API的方式发送给第三方
 * <p>
 * <b>alertNotification</b>
 */
@RequiredArgsConstructor
@LiteflowComponent(id = "alertNotification", name = "通知组件")
public class AlertNotificationCmp extends ChisNodeComponent<AlertGlobalContext, AlertCmpContext> {
    private final AlertRecordNoticeLogService alertRecordNoticeLogService;

    @Override
    public void doProcess() {
        // 获取预警处理结果
        List<Object> inputDataList = getInputDataByPath("tagData(" + config.getStr("genAlertProcessDataCmpTag") + ", result.getRecordDTOList()");
        List<AlertRecordDTO> alertRecordList = Convert.toList(AlertRecordDTO.class, inputDataList);
        if (CollUtil.isEmpty(alertRecordList)) {
            logger.warn("通知组件输入数据为空，nodeId: {}, tag: {}", nodeId, tag);
            return;
        }

        String contentTemplate = config.getStr("contentTemplate", "");
        List<AlertRecordNoticeLogPO> noticeLogList = new ArrayList<>();
        for (AlertRecordDTO alertRecordDTO : alertRecordList) {
            ContextHelper.setContextData(this.getTag(), "optData", alertRecordDTO, cmp);
            // 生成通知内容
            String noticeContent = getValueByExpression(contentTemplate);
            // 创建通知日志记录
            noticeLogList.add(createNoticeLog(alertRecordDTO, noticeContent));
        }
        // 发送通知到第三方API
        String result = sendNotificationToApi(noticeLogList);
        noticeLogList.forEach(noticeLog -> noticeLog.setNoticeResult(result));
        alertRecordNoticeLogService.saveBatch(noticeLogList);
    }

    @Override
    public String[] getMustConfigList() {
        return new String[]{"inputDataPath", "contentTemplate", "apiUrl"};
    }

    /**
     * 创建通知日志记录
     *
     * @param alertRecordDTO 预警记录DTO
     * @param noticeContent  通知内容
     * @return 通知日志记录
     */
    private AlertRecordNoticeLogPO createNoticeLog(AlertRecordDTO alertRecordDTO, String noticeContent) {
        AlertRecordNoticeLogPO noticeLog = new AlertRecordNoticeLogPO();
        // 设置关联ID
        noticeLog.setAlertRecordId(Convert.toStr(alertRecordDTO.getAlertRecordId()));
        // 复制冗余字段
        noticeLog.setRuleCode(alertRecordDTO.getRuleCode());
        noticeLog.setDeDupValue(alertRecordDTO.getDeDupValue());
        noticeLog.setChangeType(alertRecordDTO.getChangeType());
        noticeLog.setAlertLevel(alertRecordDTO.getAlertLevel());
        noticeLog.setAlertValue(alertRecordDTO.getAlertValue());
        noticeLog.setStatus(alertRecordDTO.getStatus());
        noticeLog.setAlertContent(alertRecordDTO.getAlertContent());
        noticeLog.setAlertJson(Convert.toStr(alertRecordDTO.getAlertJson(), ""));
        noticeLog.setSourceData(Convert.toStr(alertRecordDTO.getSourceData(), ""));
        // 设置通知内容
        noticeLog.setNoticeContent(noticeContent);
        return noticeLog;
    }

    /**
     * 发送通知到第三方API
     *
     * @param noticeLogList 通知
     */
    private String sendNotificationToApi(List<AlertRecordNoticeLogPO> noticeLogList) {
        String apiUrl = config.getStr("apiUrl");
        String method = config.getStr("method", "POST");
        int timeout = config.getInt("timeout", 30000);

        // 构建API请求参数
        Map<String, Object> params = BaseApiNodeUtils.buildApiParams(this.tag, config, contextBeanList);
        params.put("notice", noticeLogList);
        // 构建请求头
        Map<String, String> headers = BaseApiNodeUtils.buildApiHeaders(this.tag, config, contextBeanList);

        // 调用API
        return BaseApiNodeUtils.executeApiCall(apiUrl, method, params, headers, timeout, this.getTag());
    }
}
