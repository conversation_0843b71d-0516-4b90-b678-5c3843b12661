package com.chis.zyjk.bigdata.timer.jobhandler;

import com.chis.project.frame.common.tools.json.JSONUtil;
import com.chis.zyjk.bigdata.timer.jobhandler.param.DwCleaningJobParam;
import com.chis.zyjk.bigdata.timer.jobhandler.result.DwCleaningJobResult;
import com.chis.zyjk.bigdata.timer.service.DwTmpConfigService;
import com.chis.zyjk.schdulejob.ScheduleJobExecutor;
import com.chis.zyjk.schdulejob.service.ScheduleJobService;
import com.way.common.core.exception.ServiceException;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 数仓分层数据清洗定时任务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DwTmpConfigJobHandler extends ScheduleJobExecutor<DwCleaningJobParam, DwCleaningJobResult> {

    private final ScheduleJobService scheduleJobService;

    private final DwTmpConfigService dwTmpConfigService;

    /**
     * 数据清洗任务
     */
    @XxlJob("bigdataCleaningJobHandler")
    public void dataCleaningJobHandler() {
        try {
            String paramStr = XxlJobHelper.getJobParam();
            DwCleaningJobParam param = JSONUtil.toBean(paramStr, DwCleaningJobParam.class);
            if (param == null) {
                throw new ServiceException("任务参数解析失败");
            }

            // 开始执行任务
            start(param);
        } catch (Exception e) {
            log.error("分层清洗任务异常：{}", e.getMessage());
            XxlJobHelper.log("分层清洗任务异常：{}", e.getMessage());
            XxlJobHelper.handleFail(e.getMessage());
        }
    }


    @Override
    protected ScheduleJobService getScheduleJobService() {
        return scheduleJobService;
    }

    @Override
    protected String getJobName() {
        return "dw_bigdata_cleaning_";
    }

    @Override
    protected DwCleaningJobResult execute(String fullJobName, DwCleaningJobParam jobParam, DwCleaningJobResult dwCleaningJobResult) {
        try {
            return dwTmpConfigService.handlerTmpConfig(fullJobName, dwCleaningJobResult);
        } catch (Exception e) {
            log.error("分层任务执行异常：{}", e.getMessage(), e);
            XxlJobHelper.log("分层任务执行异常：{}", e.getMessage());
            scheduleJobService.failure(fullJobName, e.getMessage());
            throw new ServiceException("分层任务执行异常");
        }
    }

    @Override
    protected Class<DwCleaningJobResult> getJobResultClazz() {
        return DwCleaningJobResult.class;
    }

    @Override
    protected String initResult(DwCleaningJobParam jobParam) {
        DwCleaningJobResult dwCleaningJobResult = new DwCleaningJobResult();
        return JSONUtil.toJsonStr(dwCleaningJobResult);
    }
}
