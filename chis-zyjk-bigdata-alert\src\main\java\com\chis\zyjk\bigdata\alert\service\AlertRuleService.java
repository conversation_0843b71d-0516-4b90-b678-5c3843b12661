package com.chis.zyjk.bigdata.alert.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chis.zyjk.bigdata.alert.mapper.AlertRuleMapper;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRulePO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 预警规则服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlertRuleService {

    private final AlertRuleMapper alertRuleMapper;

    /**
     * 根据ID查询预警规则
     *
     * @param id 主键ID
     * @return 预警规则
     */
    public AlertRulePO getById(String id) {
        return alertRuleMapper.selectById(id);
    }

    /**
     * 根据规则编码查询预警规则
     *
     * @param ruleCode 规则编码
     * @return 预警规则
     */
    public AlertRulePO getByRuleCode(String ruleCode) {
        LambdaQueryWrapper<AlertRulePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlertRulePO::getRuleCode, ruleCode)
                .eq(AlertRulePO::getDelFlag, "0");
        return alertRuleMapper.selectOne(queryWrapper);
    }

    /**
     * 分页查询预警规则
     *
     * @param page 分页参数
     * @param ruleCode 规则编码（可选）
     * @param ruleName 规则名称（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    public Page<AlertRulePO> page(Page<AlertRulePO> page, String ruleCode, String ruleName, Integer status) {
        LambdaQueryWrapper<AlertRulePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(ruleCode != null, AlertRulePO::getRuleCode, ruleCode)
                .like(ruleName != null, AlertRulePO::getRuleName, ruleName)
                .eq(status != null, AlertRulePO::getStatus, status)
                .eq(AlertRulePO::getDelFlag, "0")
                .orderByDesc(AlertRulePO::getUpdateTime);
        return alertRuleMapper.selectPage(page, queryWrapper);
    }

    /**
     * 保存预警规则
     *
     * @param alertRule 预警规则
     * @return 是否成功
     */
    public boolean save(AlertRulePO alertRule) {
        return alertRuleMapper.insert(alertRule) > 0;
    }

    /**
     * 更新预警规则
     *
     * @param alertRule 预警规则
     * @return 是否成功
     */
    public boolean updateById(AlertRulePO alertRule) {
        return alertRuleMapper.updateById(alertRule) > 0;
    }

    /**
     * 逻辑删除预警规则
     *
     * @param id 主键ID
     * @return 是否成功
     */
    public boolean removeById(String id) {
        LambdaUpdateWrapper<AlertRulePO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(AlertRulePO::getDelFlag, "1")
                .eq(AlertRulePO::getId, id);
        return alertRuleMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 更新规则状态
     *
     * @param id 主键ID
     * @param status 状态
     * @return 是否成功
     */
    public boolean updateStatus(String id, Integer status) {
        LambdaUpdateWrapper<AlertRulePO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(AlertRulePO::getStatus, status)
                .eq(AlertRulePO::getId, id);
        return alertRuleMapper.update(null, updateWrapper) > 0;
    }
}
