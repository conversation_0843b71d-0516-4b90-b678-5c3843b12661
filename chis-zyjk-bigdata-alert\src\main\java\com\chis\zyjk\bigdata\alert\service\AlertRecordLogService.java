package com.chis.zyjk.bigdata.alert.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chis.zyjk.bigdata.alert.mapper.AlertRecordLogMapper;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRecordLogPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 预警记录预警子服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlertRecordLogService {

    private final AlertRecordLogMapper alertRecordLogMapper;

    /**
     * 根据ID查询预警记录日志
     *
     * @param id 主键ID
     * @return 预警记录日志
     */
    public AlertRecordLogPO getById(String id) {
        return alertRecordLogMapper.selectById(id);
    }

    /**
     * 根据预警记录ID查询日志列表
     *
     * @param alertRecordId 预警记录ID
     * @return 日志列表
     */
    public List<AlertRecordLogPO> listByAlertRecordId(String alertRecordId) {
        LambdaQueryWrapper<AlertRecordLogPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlertRecordLogPO::getAlertRecordId, alertRecordId)
                .eq(AlertRecordLogPO::getDelFlag, "0")
                .orderByDesc(AlertRecordLogPO::getCreateTime);
        return alertRecordLogMapper.selectList(queryWrapper);
    }

    /**
     * 根据规则编码查询日志列表
     *
     * @param ruleCode 规则编码
     * @return 日志列表
     */
    public List<AlertRecordLogPO> listByRuleCode(String ruleCode) {
        LambdaQueryWrapper<AlertRecordLogPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlertRecordLogPO::getRuleCode, ruleCode)
                .eq(AlertRecordLogPO::getDelFlag, "0")
                .orderByDesc(AlertRecordLogPO::getCreateTime);
        return alertRecordLogMapper.selectList(queryWrapper);
    }

    /**
     * 根据规则编码和去重键查询日志列表
     *
     * @param ruleCode   规则编码
     * @param deDupValue 去重键
     * @return 日志列表
     */
    public List<AlertRecordLogPO> listByRuleCodeAndDeDupValue(String ruleCode, String deDupValue) {
        LambdaQueryWrapper<AlertRecordLogPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlertRecordLogPO::getRuleCode, ruleCode)
                .eq(AlertRecordLogPO::getDeDupValue, deDupValue)
                .eq(AlertRecordLogPO::getDelFlag, "0")
                .orderByDesc(AlertRecordLogPO::getCreateTime);
        return alertRecordLogMapper.selectList(queryWrapper);
    }

    /**
     * 分页查询预警记录日志
     *
     * @param page          分页参数
     * @param alertRecordId 预警记录ID（可选）
     * @param ruleCode      规则编码（可选）
     * @param changeType    变更类型（可选）
     * @param alertLevel    预警级别（可选）
     * @return 分页结果
     */
    public Page<AlertRecordLogPO> page(Page<AlertRecordLogPO> page, String alertRecordId, String ruleCode,
                                       String changeType, String alertLevel) {
        LambdaQueryWrapper<AlertRecordLogPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(alertRecordId != null, AlertRecordLogPO::getAlertRecordId, alertRecordId)
                .like(ruleCode != null, AlertRecordLogPO::getRuleCode, ruleCode)
                .eq(changeType != null, AlertRecordLogPO::getChangeType, changeType)
                .eq(alertLevel != null, AlertRecordLogPO::getAlertLevel, alertLevel)
                .eq(AlertRecordLogPO::getDelFlag, "0")
                .orderByDesc(AlertRecordLogPO::getCreateTime);
        return alertRecordLogMapper.selectPage(page, queryWrapper);
    }

    /**
     * 保存预警记录日志
     *
     * @param alertRecordLog 预警记录日志
     * @return 是否成功
     */
    public boolean save(AlertRecordLogPO alertRecordLog) {
        return alertRecordLogMapper.insert(alertRecordLog) > 0;
    }

    /**
     * 批量保存预警记录日志（真正的分批处理，每批最大200条）
     *
     * @param alertRecordLogs 预警记录日志列表
     * @return 是否成功
     */
    public boolean saveBatch(List<AlertRecordLogPO> alertRecordLogs) {
        if (alertRecordLogs == null || alertRecordLogs.isEmpty()) {
            return true;
        }

        final int BATCH_SIZE = 200;
        int totalSize = alertRecordLogs.size();

        log.info("开始批量保存预警记录日志，总数: {}, 批次大小: {}", totalSize, BATCH_SIZE);

        // 分批处理
        for (int i = 0; i < totalSize; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, totalSize);
            List<AlertRecordLogPO> batch = alertRecordLogs.subList(i, endIndex);

            log.debug("处理批次 {}-{}/{}", i + 1, endIndex, totalSize);

            // 批量保存当前批次
            for (AlertRecordLogPO alertRecordLog : batch) {
                alertRecordLogMapper.insert(alertRecordLog);
            }
        }

        log.info("批量保存预警记录日志完成，总数: {}", totalSize);
        return true;
    }

    /**
     * 更新预警记录日志
     *
     * @param alertRecordLog 预警记录日志
     * @return 是否成功
     */
    public boolean updateById(AlertRecordLogPO alertRecordLog) {
        return alertRecordLogMapper.updateById(alertRecordLog) > 0;
    }
}
