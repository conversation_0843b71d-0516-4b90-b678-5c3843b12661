package com.chis.zyjk.bigdata.common.pojo.vo.dataclean;


import com.chis.zyjk.bigdata.common.enums.ExecuteStatusEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/7/8 16:00
 */
@Data
public class DataCleaningVO implements Serializable {

    /**
     * 执行时间
     */
    private Long executeTime;

    /**
     * 执行状态 1：成功 2：失败
     */
    private Integer executeStatus = ExecuteStatusEnum.FAIL.getCode();

    /**
     * 数仓编码
     */
    private String dwCode;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 返回参数（时间）
     */
    private String returnParam;
}
