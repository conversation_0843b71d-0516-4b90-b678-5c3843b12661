package com.chis.zyjk.bigdata.alert.liteflow.cmp;

import com.chis.project.frame.common.tools.core.convert.Convert;
import com.chis.project.frame.common.tools.core.util.ObjectUtil;
import com.chis.project.frame.common.tools.core.util.StrUtil;
import com.chis.project.frame.common.tools.json.JSONArray;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.zyjk.bigdata.alert.enums.ProcessStrategy;
import com.chis.zyjk.bigdata.alert.liteflow.common.ContextHelper;
import com.chis.zyjk.bigdata.alert.liteflow.core.ChisNodeComponent;
import com.chis.zyjk.bigdata.alert.liteflow.utils.SpELExpressionUtils;
import com.chis.zyjk.bigdata.alert.liteflow.utils.VariableReplacerUtils;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertCmpContext;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertGlobalContext;
import com.chis.zyjk.bigdata.alert.pojo.dto.AlertProcessResultDTO;
import com.chis.zyjk.bigdata.alert.pojo.dto.AlertRecordDTO;
import com.chis.zyjk.bigdata.alert.service.AlertRecordService;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * 预警组件
 * <p>
 * <b>alert</b>
 */
@RequiredArgsConstructor
@LiteflowComponent(id = "alert", name = "预警组件")
public class AlertCmp extends ChisNodeComponent<AlertGlobalContext, AlertCmpContext> {

    private final AlertRecordService alertRecordService;

    @Override
    public void doProcess() {
        // 获取预警处理结果
        String alertProcessDataPath = "tagData(" + config.getStr("genAlertProcessDataCmpTag") + ", result)";
        Object alertProcessDataObject = VariableReplacerUtils.resolveExpression(this.tag, alertProcessDataPath, contextBeanList);
        AlertProcessResultDTO alertProcessData = Convert.convert(AlertProcessResultDTO.class, alertProcessDataObject);
        if (ObjectUtil.isEmpty(alertProcessData) || ObjectUtil.isEmpty(alertProcessData.getAlertRecordMap())) {
            return;
        }

        // 处理所有记录
        alertProcessData.getAlertRecordMap().forEach(
                (deDupValue, alertRecord) -> dealAlertRecord(alertProcessData, alertRecord)
        );

        // 批量保存或更新实体
        alertRecordService.batchSaveOrUpdateEntities(alertProcessData.getRecordDTOList());
        System.out.println();
    }

    /**
     * 处理批量记录
     *
     * @param alertProcessData 预处理数据
     * @param alertRecord      预处理预警列表
     */
    private void dealAlertRecord(AlertProcessResultDTO alertProcessData, List<AlertRecordDTO> alertRecord) {
        alertRecord.forEach(alertRecordDTO -> processRecordWithExisting(alertProcessData, alertRecordDTO));
    }

    /**
     * 处理单条记录
     *
     * @param alertProcessData 预处理数据
     * @param alertRecordDTO   预处理预警数据
     */
    private void processRecordWithExisting(AlertProcessResultDTO alertProcessData, AlertRecordDTO alertRecordDTO) {
        // 决定处理策略并直接封装alertRecordDTO
        ProcessStrategy strategy = determineStrategyAndFillConfig(alertProcessData, alertRecordDTO);
        executeStrategyAction(alertProcessData, alertRecordDTO, strategy);
    }

    /**
     * 决定处理策略并填充配置信息
     *
     * @param alertProcessData 预处理数据
     * @param alertRecordDTO   预处理预警数据
     * @return 处理策略
     */
    private ProcessStrategy determineStrategyAndFillConfig(AlertProcessResultDTO alertProcessData, AlertRecordDTO alertRecordDTO) {
        JSONObject alertStrategy = config.getJSONObject("alertStrategy");
        // 无策略配置时的默认逻辑
        if (alertStrategy == null) {
            return ProcessStrategy.IGNORE;
        }

        // 直接执行策略链并填充配置
        return executeStrategyChainAndFillConfig(alertStrategy, alertProcessData, alertRecordDTO);
    }

    /**
     * 执行策略链并填充配置
     *
     * @param alertStrategy    升级策略配置
     * @param alertProcessData 预处理数据
     * @param alertRecordDTO   预处理预警数据
     * @return 处理策略
     */
    private ProcessStrategy executeStrategyChainAndFillConfig(JSONObject alertStrategy, AlertProcessResultDTO alertProcessData, AlertRecordDTO alertRecordDTO) {
        JSONArray strategies = alertStrategy.getJSONArray("strategies");
        if (ObjectUtil.isEmpty(strategies)) {
            return ProcessStrategy.IGNORE;
        }
        ContextHelper.setContextData(this.getTag(), "optData", alertRecordDTO, cmp);

        // 按顺序执行策略，直到返回非NO_ACTION
        for (int i = 0; i < strategies.size(); i++) {
            JSONObject strategy = strategies.getJSONObject(i);
            ProcessStrategy result = executeStrategyAndFillConfig(strategy, alertProcessData, alertRecordDTO);

            // 只有返回NO_ACTION时才继续下一个策略
            if (result != ProcessStrategy.NO_ACTION) {
                return result;
            }
        }

        // 所有策略都返回NO_ACTION
        return ProcessStrategy.NO_ACTION;
    }

    /**
     * 执行单个策略并填充配置
     *
     * @param strategy         策略配置
     * @param alertProcessData 预处理数据
     * @param alertRecordDTO   预处理预警数据
     * @return 处理策略
     */
    private ProcessStrategy executeStrategyAndFillConfig(JSONObject strategy, AlertProcessResultDTO alertProcessData, AlertRecordDTO alertRecordDTO) {
        String strategyType = strategy.getStr("type");
        JSONObject strategyConfig = strategy.getJSONObject("config");

        switch (strategyType) {
            case "CONDITION_BASED":
                return executeConditionBasedStrategyAndFillConfig(strategyConfig, alertProcessData, alertRecordDTO);
            case "FIXED":
                Object actionObj = strategyConfig.get("action");
                if (actionObj instanceof JSONObject) {
                    // 包含配置的动作对象，直接填充配置
                    JSONObject actionConfig = (JSONObject) actionObj;
                    ProcessStrategy actionStrategy = ProcessStrategy.valueOf(actionConfig.getStr("action", "NO_ACTION"));
                    if (actionStrategy != ProcessStrategy.NO_ACTION && actionStrategy != ProcessStrategy.IGNORE) {
                        fillAlertRecordConfig(alertRecordDTO, actionConfig);
                    }
                    return actionStrategy;
                } else {
                    // 简单字符串动作，无额外配置
                    String action = strategyConfig.getStr("action", "NO_ACTION");
                    return ProcessStrategy.valueOf(action);
                }
            default:
                return ProcessStrategy.NO_ACTION;
        }
    }

    /**
     * 执行条件表达式策略并填充配置
     *
     * @param strategyConfig   策略配置
     * @param alertProcessData 预处理数据
     * @param alertRecordDTO   预处理预警数据
     * @return 处理策略
     */
    private ProcessStrategy executeConditionBasedStrategyAndFillConfig(JSONObject strategyConfig, AlertProcessResultDTO alertProcessData, AlertRecordDTO alertRecordDTO) {
        String condition = strategyConfig.getStr("condition");
        JSONObject actions = strategyConfig.getJSONObject("actions");

        condition = VariableReplacerUtils.replaceVariables(this.tag, condition, contextBeanList);
        // 使用SpEL 表达式工具类评估条件
        boolean matches = SpELExpressionUtils.evaluateCondition(condition, contextBeanList);

        Object actionObj = actions.get(matches ? "true" : "false");
        if (actionObj instanceof JSONObject) {
            // 包含配置的动作对象，直接填充配置
            JSONObject actionConfig = (JSONObject) actionObj;
            ProcessStrategy actionStrategy = ProcessStrategy.valueOf(actionConfig.getStr("action", "NO_ACTION"));
            if (actionStrategy != ProcessStrategy.NO_ACTION && actionStrategy != ProcessStrategy.IGNORE) {
                fillAlertRecordConfig(alertRecordDTO, actionConfig);
            }
            return actionStrategy;
        } else {
            // 简单字符串动作，无额外配置
            String actionStr = actions.getStr(matches ? "true" : "false", "NO_ACTION");
            return ProcessStrategy.valueOf(actionStr);
        }
    }

    /**
     * 填充预警记录配置信息
     *
     * @param alertRecordDTO 预处理预警数据
     * @param actionConfig   动作配置
     */
    private void fillAlertRecordConfig(AlertRecordDTO alertRecordDTO, JSONObject actionConfig) {
        // 使用配置中的alertLevel，如果为空则保持原值
        if (StrUtil.isNotBlank(actionConfig.getStr("alertLevel"))) {
            alertRecordDTO.setAlertLevel(actionConfig.getStr("alertLevel"));
        } else {
            alertRecordDTO.setAlertLevel(alertRecordDTO.getAlertLevel());
        }

        // 使用配置中的alertJson，如果为空则保持原值
        if (actionConfig.getJSONObject("alertJson") != null) {
            alertRecordDTO.setAlertJson(actionConfig.getJSONObject("alertJson"));
        } else {
            alertRecordDTO.setAlertJson(new JSONObject());
        }

        // 使用配置中的alertContentTemplate生成预警内容
        if (StrUtil.isNotBlank(actionConfig.getStr("alertContentTemplate"))) {
            String alertContent = getValueByExpression(actionConfig.getStr("alertContentTemplate"));
            alertRecordDTO.setAlertContent(alertContent);
        } else {
            alertRecordDTO.setAlertContent(alertRecordDTO.getAlertContent());
        }
    }

    /**
     * 执行策略动作
     *
     * @param alertProcessData 预处理数据
     * @param alertRecordDTO   预处理预警数据
     * @param strategy         处理策略
     */
    private void executeStrategyAction(AlertProcessResultDTO alertProcessData, AlertRecordDTO alertRecordDTO, ProcessStrategy strategy) {
        switch (strategy) {
            case CREATE:
                alertRecordDTO.setChangeType(ProcessStrategy.CREATE.getCode());
                alertProcessData.getRecordDTOList().add(alertRecordDTO);
                break;
            case UPDATE:
                if (ObjectUtil.isEmpty(alertRecordDTO.getAlertRecord())) {
                    alertRecordDTO.setChangeType(ProcessStrategy.CREATE.getCode());
                } else {
                    alertRecordDTO.setChangeType(ProcessStrategy.UPDATE.getCode());
                }
                alertProcessData.getRecordDTOList().add(alertRecordDTO);
                break;
            case CLOSE:
                alertRecordDTO.setChangeType(ProcessStrategy.CLOSE.getCode());
                alertProcessData.getRecordDTOList().add(alertRecordDTO);
                break;
            default:
        }
    }

    @Override
    public String[] getMustConfigList() {
        return new String[]{"genAlertProcessDataCmpTag", "alertStrategy"};
    }

}
