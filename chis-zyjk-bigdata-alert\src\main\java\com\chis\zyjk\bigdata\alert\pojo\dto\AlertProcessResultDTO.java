package com.chis.zyjk.bigdata.alert.pojo.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 预警处理结果类
 */
@Data
public class AlertProcessResultDTO {

    /**
     * 预警记录DTO映射
     * key: 预警去重键值
     * value: 预处理预警记录DTO
     */
    private Map<String, List<AlertRecordDTO>> alertRecordMap = new HashMap<>();

    /**
     * 预处理预警记录DTO
     */
    private List<AlertRecordDTO> recordDTOList = new ArrayList<>();
}
