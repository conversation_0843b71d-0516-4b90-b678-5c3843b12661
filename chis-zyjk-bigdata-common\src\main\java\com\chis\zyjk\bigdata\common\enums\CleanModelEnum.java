package com.chis.zyjk.bigdata.common.enums;

import com.chis.zyjk.core.common.enums.base.NumericCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 执行转态枚举
 */
@Getter
@AllArgsConstructor
public enum CleanModelEnum implements NumericCodeEnum {
    BATCH(1, "批处理"),
    MATERIALIZED_VIEW(2, "物化视图"),
    OTHER(3, "其他");

    private final Integer code;
    private final String desc;

    @Override
    public String getEnumDesc() {
        return "清洗模式";
    }
}
