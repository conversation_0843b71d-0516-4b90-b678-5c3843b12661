package com.chis.zyjk.bigdata.api.dataserver.service;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.chis.zyjk.bigdata.api.dataserver.mapper.DwDorisDao;
import com.chis.zyjk.bigdata.common.enums.ExecuteStatusEnum;
import com.chis.zyjk.bigdata.common.pojo.dto.dataclean.DataCleaningDTO;
import com.chis.zyjk.bigdata.common.pojo.vo.dataclean.DataCleaningVO;
import com.way.common.core.exception.ServiceException;
import com.way.common.core.utils.bean.BeanUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class DwCleaningDataService {

    private final DwDorisDao dwDorisDao;
    private static final String FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 清洗数据
     */
    public DataCleaningVO cleanTableData(DataCleaningDTO dataCleaningDTO) {
        DataCleaningVO dataCleaningVO = new DataCleaningVO();
        BeanUtils.copyProperties(dataCleaningDTO, dataCleaningVO);
        String tableName = dataCleaningDTO.getTableName().toLowerCase();
        // 清洗数据库名称
        if (!this.dwDorisDao.tableExists(tableName)) {
            throw new ServiceException("目标数据库（" + tableName + "）不存在！");
        }
        String sql;
        try {
            if (dataCleaningDTO.getEncodeFlag()) {
                // 远程调用 sql 符号转换，需要加密
                String decodedSql = URLDecoder.decode(dataCleaningDTO.getExecuteSql(), "UTF-8");
                sql = decodedSql.replace("\r\n", " ").replace("\n", " ");
            } else {
                sql = dataCleaningDTO.getExecuteSql().replace("\r\n", " ").replace("\n", " ");
            }
        } catch (UnsupportedEncodingException e) {
            throw new ServiceException("编码解码失败");
        }

        // 清洗数据不分页，利用 doris 本身高性能
        long startTimeMillis = System.currentTimeMillis();
        LocalDateTime lastTime = LocalDateTimeUtil.parse(dataCleaningDTO.getExecuteParam(), FORMAT);
        dwDorisDao.cleanBusinessData(sql, lastTime);
        // 获取目标库最大时间挫
        LocalDateTime localDateTime = dwDorisDao.targetTableNameMaxTime(tableName);
        long duration = System.currentTimeMillis() - startTimeMillis;
        log.info("同步目标库：{}，耗时：{}ms", tableName, duration);

        dataCleaningVO.setReturnParam(LocalDateTimeUtil.format(localDateTime, FORMAT));
        dataCleaningVO.setExecuteTime(duration);
        dataCleaningVO.setExecuteStatus(ExecuteStatusEnum.SUCCESS.getCode());
        return dataCleaningVO;
    }
}
