package com.chis.zyjk.bigdata.api.dataserver;

import com.way.common.security.annotation.EnableCustomConfig;
import com.way.common.security.annotation.EnableRyFeignClients;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <AUTHOR>
 * @since 2025/7/8 14:40
 */
@EnableCustomConfig
@EnableRyFeignClients(basePackages = {"com.way.system", "com.way.auth", "com.chis.zyjk.common.remote"})
@SpringBootApplication(scanBasePackages = {"com.chis.zyjk", "com.way.common", "com.way.system"})
@MapperScan(basePackages = {"com.chis.zyjk.**.mapper"})
public class ChisZyjkBigDataApiDataserverApplication {
    public static void main(String[] args) {
        SpringApplication.run(ChisZyjkBigDataApiDataserverApplication.class, args);
    }
}
