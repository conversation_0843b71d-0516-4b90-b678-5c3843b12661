package com.chis.zyjk.bigdata.api.dataserver.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * Druid数据源配置
 */
@Configuration
public class DruidDataSourceConfig {

    /**
     * 创建Doris数据源
     */
    @Bean(name = "dorisDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.druid.datasource.doris")
    public DataSource dorisDataSource() {
        return new DruidDataSource();
    }

    /**
     * 创建Doris JdbcTemplate
     */
    @Bean(name = "dorisJdbcTemplate")
    public JdbcTemplate dorisJdbcTemplate(@Qualifier("dorisDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
}
