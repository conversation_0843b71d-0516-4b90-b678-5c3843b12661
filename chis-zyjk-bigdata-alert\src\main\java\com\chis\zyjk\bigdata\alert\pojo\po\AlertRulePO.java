package com.chis.zyjk.bigdata.alert.pojo.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chis.zyjk.core.common.pojo.ZyjkPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预警规则主表
 */
@Data
@TableName("alert_rule")
@EqualsAndHashCode(callSuper = false)
public class AlertRulePO extends ZyjkPO {

    /**
     * 规则编码
     */
    private String ruleCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * Cron表达式
     */
    private String cronExpression;

    /**
     * 去重主键表达式 如{org_id}_{person_id}
     */
    private String deDupKeyExpression;

    /**
     * 预警值表达式 如{org_id}_{person_id}
     */
    private String alertValueExpression;

    /**
     * 流程配置表达式
     */
    private String liteFlowConfig;

    /**
     * 节点配置json
     */
    private String liteFlowNodeConfig;

    /**
     * 是否启用:1:启用 0:停用
     */
    private Integer ifEnable;

    /**
     * xxljob任务ID
     */
    private Integer xxljobTaskId;

    /**
     * 执行状态:0:待执行 1:执行中 2:异常
     */
    private Integer status;

    /**
     * 异常原因
     */
    private String errorMsg;
}
