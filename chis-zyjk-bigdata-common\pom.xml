<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.chis.zyjk</groupId>
        <artifactId>chis-zyjk-bigdata-platform</artifactId>
        <version>1.0.0.ALPHA</version>
    </parent>

    <artifactId>chis-zyjk-bigdata-common</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.chis.zyjk</groupId>
            <artifactId>chis-zyjk-core-common</artifactId>
            <version>1.0.0.ALPHA</version>
        </dependency>
        <!-- Chis Zyjk Core Common -->
        <dependency>
            <groupId>com.chis.frame.base</groupId>
            <artifactId>chis-way-cloud-service-base</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.chis.frame.base</groupId>
            <artifactId>chis-way-api-system</artifactId>
        </dependency>
        <!-- SpringCloud Openfeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
            <scope>compile</scope>
        </dependency>

        <!-- Mapstruct -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <version>0.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
    </dependencies>

</project>