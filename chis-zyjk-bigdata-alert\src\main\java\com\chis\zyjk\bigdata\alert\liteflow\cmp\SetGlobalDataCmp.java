package com.chis.zyjk.bigdata.alert.liteflow.cmp;

import com.chis.zyjk.bigdata.alert.enums.DataType;
import com.chis.zyjk.bigdata.alert.liteflow.common.ContextHelper;
import com.chis.zyjk.bigdata.alert.liteflow.core.ChisNodeComponent;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowErrorCode;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowExceptionHelper;
import com.chis.zyjk.bigdata.alert.liteflow.utils.VariableReplacerUtils;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertCmpContext;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertGlobalContext;
import com.chis.zyjk.core.common.enums.base.StringCodeEnum;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.RequiredArgsConstructor;

/**
 * 设置全局参数组件
 * <p>
 * <b>setGlobalData</b>
 */
@RequiredArgsConstructor
@LiteflowComponent(id = "setGlobalData", name = "设置全局参数组件")
public class SetGlobalDataCmp extends ChisNodeComponent<AlertGlobalContext, AlertCmpContext> {
    @Override
    public void doProcess() {
        // 获取数据类型
        DataType dataType;
        try {
            dataType = StringCodeEnum.fromCodeWithException(DataType.class, data.getStr("type"));
        } catch (Exception e) {
            throw LiteFlowExceptionHelper.createNodeException(
                    e,
                    LiteFlowErrorCode.CMP_PARAMETER_ERROR,
                    this
            );
        }

        String dataName = config.getStr("dataName");
        String path = "global" + ContextHelper.DATA_CONTEXT_PATH + "." + dataName;
        Object dataObject = config.get("data");
        switch (dataType) {
            case STRING:
                ContextHelper.setContextData(path, getValueByExpression(dataObject.toString()), cmp);
                break;
            case PATH:
                Object data = VariableReplacerUtils.resolveExpression(this.tag, dataObject.toString(), contextBeanList);
                ContextHelper.setContextData(path, data, cmp);
                break;
            case JSON:
                ContextHelper.setContextData(path, dataObject, cmp);
                break;
        }
    }

    @Override
    public String[] getMustConfigList() {
        return new String[]{"type", "dataName", "data"};
    }
}
