package com.chis.zyjk.bigdata.alert.liteflow.utils;

import cn.hutool.core.lang.Tuple;
import com.chis.project.frame.common.tools.core.convert.Convert;
import com.chis.project.frame.common.tools.json.JSONArray;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.yomahub.liteflow.slot.DefaultContext;
import com.yomahub.liteflow.util.LiteflowContextRegexMatcher;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 变量替换工具类
 * <p>
 * 支持的变量语法：
 * <p>- ${global(field)} - 引用全局上下文属性
 * <p>- ${tag.currentItem.field} - 引用指定节点的当前项数据
 * <p>- ${tag.currentData} - 引用指定节点的当前批次数据
 * <p>- ${tag.loopIndex} - 引用指定节点的循环索引
 * <p>- ${uuid()} - 生成UUID
 * <p>- ${timestamp()} - 生成时间戳
 * <p>- ${date('yyyy-MM-dd')} - 格式化当前日期
 */
@Slf4j
public final class VariableReplacerUtils {

    // 变量表达式正则模式
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");

    // 函数调用正则模式
    private static final Pattern FUNCTION_PATTERN = Pattern.compile("(\\w+)\\(([^)]*)\\)");

    /**
     * 替换字符串中的所有变量
     *
     * @param tag      tag
     * @param template 包含变量的模板字符串
     * @param context  LiteFlow上下文
     * @return 替换后的字符串
     */
    public static String replaceVariables(String tag, String template, List<Tuple> context) {
        return replaceVariables(tag, template, context, null);
    }

    /**
     * 替换字符串中的所有变量
     *
     * @param tag          tag
     * @param template     包含变量的模板字符串
     * @param context      LiteFlow上下文
     * @param defaultValue 默认值
     * @return 替换后的字符串
     */
    public static String replaceVariables(String tag, String template, List<Tuple> context, String defaultValue) {
        if (template == null || template.isEmpty()) {
            return template;
        }

        Matcher matcher = VARIABLE_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String expression = matcher.group(1);
            defaultValue = Convert.toStr(defaultValue, "${" + expression + "}");
            Object value = resolveExpression(tag, expression, context);
            matcher.appendReplacement(result, Matcher.quoteReplacement(Convert.toStr(value, defaultValue)));
        }

        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 解析单个表达式
     *
     * @param tag        tag
     * @param expression 表达式内容（不包含${}）
     * @param context    LiteFlow上下文
     * @return 解析后的值
     */
    public static Object resolveExpression(String tag, String expression, List<Tuple> context) {
        // 检查是否是函数调用 - 使用新的解析方法支持嵌套括号
        String[] functionInfo = parseFunctionCall(expression);
        if (functionInfo == null) {
            return LiteflowContextRegexMatcher.searchContext(context, expression);
        }

        String functionName = functionInfo[0];
        String paramsStr = functionInfo[1];

        // 解析函数参数 - 支持多个参数，用逗号分隔
        String[] params = parseParameters(paramsStr);

        int length = params.length;
        switch (functionName) {
            case "uuid":
                return UUID.randomUUID().toString();
            case "timestamp":
                return String.valueOf(System.currentTimeMillis());
            case "date":
                String format = length > 0 ? params[0].replaceAll("'", "") : "";
                if (format.isEmpty()) {
                    format = "yyyy-MM-dd HH:mm:ss";
                }
                return LocalDateTime.now().format(DateTimeFormatter.ofPattern(format));
            case "global":
                if (length == 0) {
                    expression = "global";
                } else {
                    expression = "global.get" + params[0].substring(0, 1).toUpperCase() + params[0].substring(1) + "()";
                }
                return LiteflowContextRegexMatcher.searchContext(context, expression);
            case "tag":
                if (length == 0) {
                    expression = "cmp.getData('" + tag + "')";
                } else if (length > 1) {
                    expression = "cmp.getData('" + params[0] + "')." + params[1];
                } else {
                    expression = "cmp.getData('" + tag + "')." + params[0];
                }
                return LiteflowContextRegexMatcher.searchContext(context, expression);
            case "tagConfig":
                if (length == 0) {
                    expression = "cmp.getData('" + tag + "').config";
                } else if (length > 1) {
                    expression = "cmp.getData('" + params[0] + "').config." + params[1];
                } else {
                    expression = "cmp.getData('" + tag + "').config." + params[0];
                }
                return LiteflowContextRegexMatcher.searchContext(context, expression);
            case "tagData":
                if (length == 0) {
                    expression = "cmp.getData('" + tag + "').data";
                } else if (length > 1) {
                    expression = "cmp.getData('" + params[0] + "').data." + params[1];
                } else {
                    expression = "cmp.getData('" + tag + "').data." + params[0];
                }
                return LiteflowContextRegexMatcher.searchContext(context, expression);
            case "optData":
                if (length == 0) {
                    expression = "cmp.getData('" + tag + "').data.optData";
                } else {
                    expression = "cmp.getData('" + tag + "').data.optData." + params[0];
                }
                return LiteflowContextRegexMatcher.searchContext(context, expression);
            default:
                return LiteflowContextRegexMatcher.searchContext(context, expression);
        }
    }

    /**
     * 解析函数参数字符串，支持多个参数用逗号分隔
     * 处理引号内的逗号，避免错误分割
     *
     * @param paramsStr 参数字符串，如 "param1, 'param2,with,comma', param3"
     * @return 参数数组
     */
    private static String[] parseParameters(String paramsStr) {
        if (paramsStr == null || paramsStr.trim().isEmpty()) {
            return new String[0];
        }

        // 简单情况：没有引号的参数
        if (!paramsStr.contains("'") && !paramsStr.contains("\"")) {
            return paramsStr.split(",\\s*");
        }

        // 复杂情况：处理引号内的逗号
        java.util.List<String> params = new java.util.ArrayList<>();
        StringBuilder currentParam = new StringBuilder();
        boolean inQuotes = false;
        char quoteChar = 0;

        for (int i = 0; i < paramsStr.length(); i++) {
            char c = paramsStr.charAt(i);

            if (!inQuotes && (c == '\'' || c == '"')) {
                // 开始引号
                inQuotes = true;
                quoteChar = c;
                currentParam.append(c);
            } else if (inQuotes && c == quoteChar) {
                // 结束引号
                inQuotes = false;
                currentParam.append(c);
            } else if (!inQuotes && c == ',') {
                // 参数分隔符
                params.add(currentParam.toString().trim());
                currentParam = new StringBuilder();
            } else {
                currentParam.append(c);
            }
        }

        // 添加最后一个参数
        if (currentParam.length() > 0) {
            params.add(currentParam.toString().trim());
        }

        return params.toArray(new String[0]);
    }

    /**
     * 解析函数调用，支持嵌套括号
     *
     * @param expression 表达式
     * @return 长度为2的字符串数组，[0]为函数名，[1]为参数字符串；如果不是函数调用则返回null
     */
    private static String[] parseFunctionCall(String expression) {
        if (expression == null || expression.trim().isEmpty()) {
            return null;
        }

        expression = expression.trim();

        // 查找函数名和开始括号
        int openParenIndex = expression.indexOf('(');
        if (openParenIndex == -1) {
            return null; // 不是函数调用
        }

        String functionName = expression.substring(0, openParenIndex).trim();
        if (!functionName.matches("\\w+")) {
            return null; // 函数名不合法
        }

        // 查找匹配的结束括号，考虑嵌套括号
        int closeParenIndex = findMatchingCloseParen(expression, openParenIndex);
        if (closeParenIndex == -1 || closeParenIndex != expression.length() - 1) {
            return null; // 没有找到匹配的结束括号或括号后还有其他字符
        }

        // 提取参数字符串
        String paramsStr = expression.substring(openParenIndex + 1, closeParenIndex);

        return new String[]{functionName, paramsStr};
    }

    /**
     * 查找匹配的结束括号位置
     *
     * @param expression 表达式
     * @param startIndex 开始括号的位置
     * @return 匹配的结束括号位置，如果没找到返回-1
     */
    private static int findMatchingCloseParen(String expression, int startIndex) {
        int parenCount = 1; // 已经遇到了一个开始括号

        for (int i = startIndex + 1; i < expression.length(); i++) {
            char c = expression.charAt(i);
            if (c == '(') {
                parenCount++;
            } else if (c == ')') {
                parenCount--;
                if (parenCount == 0) {
                    return i; // 找到匹配的结束括号
                }
            }
        }

        return -1; // 没有找到匹配的结束括号
    }

    /**
     * 获取嵌套路径的值
     */
    public static Object getNestedValue(JSONObject json, String path) {
        String[] parts = path.split("\\.");
        Object current = json;

        for (String part : parts) {
            if (current instanceof JSONObject) {
                current = ((JSONObject) current).get(part);
            } else {
                return null;
            }
        }

        return current;
    }
}
