package com.chis.zyjk.bigdata.alert;

import com.way.common.security.annotation.EnableCustomConfig;
import com.way.common.security.annotation.EnableRyFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@EnableCustomConfig
@EnableRyFeignClients
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.chis.zyjk", "com.way.common", "com.way.system"})
public class ChisZyjkBigdataAlertApplication {
    public static void main(String[] args) {
        SpringApplication.run(ChisZyjkBigdataAlertApplication.class, args);
    }
}
