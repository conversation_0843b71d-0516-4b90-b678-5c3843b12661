package com.chis.zyjk.bigdata.alert.controller;

import com.chis.zyjk.bigdata.alert.executor.AlertFlowExecutor;
import com.chis.zyjk.bigdata.alert.liteflow.pojo.dto.AlertRuleRequestDTO;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertGlobalContext;
import com.chis.zyjk.core.common.enums.ZyjkResultEnum;
import com.way.common.core.domain.R;
import com.way.common.core.exception.ServiceException;
import com.yomahub.liteflow.flow.LiteflowResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * LiteFlow规则执行控制器
 * 提供预警规则执行的REST API接口
 *
 * <AUTHOR> Assistant
 * @since 2024-01-17
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/liteflow")
public class AlertLiteFlowController {

    private final AlertFlowExecutor executor;

    /**
     * 执行预警规则
     *
     * @param request 执行请求
     * @return 执行结果
     */
    @PostMapping("/executeRule")
    public R<Void> executeRule(@RequestBody @Validated AlertRuleRequestDTO request) {
        log.info("接收到规则执行请求，expression: {}", request.getExpression());

        AlertGlobalContext globalContext = new AlertGlobalContext();
        globalContext.setRuleCode(request.getRuleCode());
        globalContext.setRuleName(request.getRuleName());
        globalContext.setExpression(request.getExpression());
        globalContext.setDeDupKeyExpression(request.getDeDupKeyExpression());
        globalContext.setAlertValueExpression(request.getAlertValueExpression());

        // 执行规则
        LiteflowResponse response = executor.executeRule(globalContext, request.getNodeConfigs());

        if (!response.isSuccess()) {
            throw new ServiceException(response.getCause().toString(), ZyjkResultEnum.INTERNAL_SERVER_ERROR.getCode());
        }
        return R.ok();
    }

}
