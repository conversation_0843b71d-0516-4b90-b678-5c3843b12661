package com.chis.zyjk.bigdata.alert.liteflow.exception;

import com.chis.project.frame.common.tools.core.exceptions.ExceptionUtil;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.log.LFLog;
import com.yomahub.liteflow.log.LFLoggerManager;
import lombok.AllArgsConstructor;

/**
 * LiteFlow异常处理工具类
 */
@AllArgsConstructor
public class LiteFlowExceptionHelper {
    private static final LFLog logger = LFLoggerManager.getLogger(FlowExecutor.class);

    /**
     * 创建规则级别异常
     *
     * @param errorCode 错误码
     * @return 规则级别异常
     */
    public static LiteFlowRuleException createRuleException(LiteFlowErrorCode errorCode) {
        LiteFlowRuleException liteFlowRuleException = new LiteFlowRuleException(errorCode);

        logger.error(liteFlowRuleException.toString());

        return liteFlowRuleException;
    }

    /**
     * 创建规则级别异常
     *
     * @param errorCode 错误码
     * @param ruleCode  规则编码
     * @return 规则级别异常
     */
    public static LiteFlowRuleException createRuleException(LiteFlowErrorCode errorCode, String ruleCode) {
        LiteFlowRuleException liteFlowRuleException = new LiteFlowRuleException(errorCode, ruleCode);

        logger.error(liteFlowRuleException.toString());

        return liteFlowRuleException;
    }

    /**
     * 创建规则级别异常
     *
     * @param errorCode 错误码
     * @param message   自定义消息
     * @param ruleCode  规则编码
     * @return 规则级别异常
     */
    public static LiteFlowRuleException createRuleException(LiteFlowErrorCode errorCode, String message, String ruleCode) {
        LiteFlowRuleException liteFlowRuleException = new LiteFlowRuleException(errorCode, message, ruleCode);

        logger.error(liteFlowRuleException.toString());

        return liteFlowRuleException;
    }

    /**
     * 创建规则级别异常
     *
     * @param e         原始异常
     * @param errorCode 错误码
     * @param ruleCode  规则编码
     * @return 规则级别异常
     */
    public static LiteFlowRuleException createRuleException(Throwable e, LiteFlowErrorCode errorCode, String ruleCode) {
        return createRuleException(e, errorCode, errorCode.getDesc(), ruleCode);
    }

    /**
     * 创建规则级别异常
     *
     * @param e         原始异常
     * @param errorCode 错误码
     * @param message   自定义消息
     * @param ruleCode  规则编码
     * @return 规则级别异常
     */
    public static LiteFlowRuleException createRuleException(Throwable e, LiteFlowErrorCode errorCode, String message, String ruleCode) {
        LiteFlowRuleException liteFlowRuleException = new LiteFlowRuleException(errorCode, message, ruleCode, e);

        logger.error(liteFlowRuleException.toString());

        return liteFlowRuleException;
    }

    /**
     * 从NodeComponent创建组件级别异常
     *
     * @param errorCode 错误码
     * @param component 组件组件
     * @return 组件级别异常
     */
    public static LiteFlowCmpException createNodeException(LiteFlowErrorCode errorCode, NodeComponent component) {
        return createNodeException(errorCode, errorCode.getDesc(), component);
    }

    /**
     * 从NodeComponent创建组件级别异常
     *
     * @param errorCode 错误码
     * @param message   自定义消息
     * @return 组件级别异常
     */
    public static LiteFlowCmpException createNodeException(LiteFlowErrorCode errorCode, String message) {
        LiteFlowCmpException liteFlowCmpException = new LiteFlowCmpException(errorCode, message);

        logger.error(liteFlowCmpException.toString());

        return liteFlowCmpException;
    }

    /**
     * 从NodeComponent创建组件级别异常
     *
     * @param errorCode 错误码
     * @param message   自定义消息
     * @param component 组件组件
     * @return 组件级别异常
     */
    public static LiteFlowCmpException createNodeException(LiteFlowErrorCode errorCode, String message, NodeComponent component) {
        String nodeId = component.getNodeId();
        String nodeTag = component.getTag();
        String nodeType = component.getClass().getSimpleName();

        LiteFlowCmpException liteFlowCmpException = new LiteFlowCmpException(errorCode, message, nodeId, nodeTag, nodeType);

        logger.error(liteFlowCmpException.toString());

        return liteFlowCmpException;
    }

    /**
     * 从NodeComponent创建组件级别异常
     *
     * @param e         原始异常
     * @param errorCode 错误码
     * @return 组件级别异常
     */
    public static LiteFlowCmpException createNodeException(Throwable e, LiteFlowErrorCode errorCode) {
        LiteFlowCmpException liteFlowCmpException = new LiteFlowCmpException(errorCode, e);

        logger.error(liteFlowCmpException.toString());

        return liteFlowCmpException;
    }

    /**
     * 从NodeComponent创建组件级别异常
     *
     * @param e         原始异常
     * @param errorCode 错误码
     * @param component 组件组件
     * @return 组件级别异常
     */
    public static LiteFlowCmpException createNodeException(Throwable e, LiteFlowErrorCode errorCode, NodeComponent component) {
        return createNodeException(e, errorCode, errorCode.getDesc(), component);
    }

    /**
     * 从NodeComponent创建组件级别异常
     *
     * @param e         原始异常
     * @param errorCode 错误码
     * @param message   自定义消息
     * @return 组件级别异常
     */
    public static LiteFlowCmpException createNodeException(Throwable e, LiteFlowErrorCode errorCode, String message) {
        LiteFlowCmpException liteFlowCmpException = new LiteFlowCmpException(errorCode, message, e);

        logger.error(liteFlowCmpException.toString());

        return liteFlowCmpException;
    }

    /**
     * 从NodeComponent创建组件级别异常
     *
     * @param e         原始异常
     * @param errorCode 错误码
     * @param message   自定义消息
     * @param component 组件组件
     * @return 组件级别异常
     */
    public static LiteFlowCmpException createNodeException(Throwable e, LiteFlowErrorCode errorCode, String message, NodeComponent component) {
        String nodeId = component.getNodeId();
        String nodeTag = component.getTag();
        String nodeType = component.getClass().getSimpleName();

        LiteFlowCmpException liteFlowCmpException = new LiteFlowCmpException(errorCode, message, nodeId, nodeTag, nodeType, e);

        logger.error(liteFlowCmpException.toString());

        return liteFlowCmpException;
    }

    /**
     * 获取异常的根本原因
     *
     * @param e 异常
     * @return 根本原因异常
     */
    public static Throwable getRootCause(Throwable e) {
        return ExceptionUtil.getRootCause(e);
    }
}
