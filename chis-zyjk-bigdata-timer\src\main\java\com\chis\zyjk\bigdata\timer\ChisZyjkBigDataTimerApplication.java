package com.chis.zyjk.bigdata.timer;

import com.way.common.security.annotation.EnableCustomConfig;
import com.way.common.security.annotation.EnableRyFeignClients;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * <AUTHOR>
 * @since 2025/7/8 14:40
 */
@EnableCustomConfig
@EnableRyFeignClients(basePackages = {"com.way.system", "com.way.auth", "com.chis.zyjk.bigdata.common.remote"})
@SpringBootApplication(scanBasePackages = {"com.chis.zyjk", "com.way.common", "com.way.system"})
@MapperScan(basePackages = {"com.chis.zyjk.**.mapper"})
public class ChisZyjkBigDataTimerApplication {
    public static void main(String[] args) {
        SpringApplication.run(ChisZyjkBigDataTimerApplication.class, args);
    }
}
