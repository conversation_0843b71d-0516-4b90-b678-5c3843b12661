server:
  port: 20009
  servlet:
    context-path: /bigdata-api-dataserver

# Spring
spring:
  application:
    # 应用名称
    name: chis-zyjk-bigdata-api-dataservcer
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      # nacos server开启鉴权后需要配置
      username: nacos
      password: nacos
      discovery:
        namespace: dxl
        # 服务注册地址
        server-addr: 10.88.99.11:8848
      config:
        # 配置中心地址
        server-addr: 10.88.99.11:8848
        namespace: dxl
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}


# 为日志输出路径，全路径为nacos中的全路径配置logging.file: ${logging.dir}${spring.application.name}/chiscdc.log
logging:
  dir: logs/