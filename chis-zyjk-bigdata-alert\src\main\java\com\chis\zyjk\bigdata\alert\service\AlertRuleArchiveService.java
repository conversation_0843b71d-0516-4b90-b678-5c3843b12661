package com.chis.zyjk.bigdata.alert.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chis.zyjk.bigdata.alert.mapper.AlertRuleArchiveMapper;
import com.chis.zyjk.bigdata.alert.pojo.po.AlertRuleArchivePO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 预警规则归档服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlertRuleArchiveService {

    private final AlertRuleArchiveMapper alertRuleArchiveMapper;

    /**
     * 根据ID查询归档记录
     *
     * @param id 主键ID
     * @return 归档记录
     */
    public AlertRuleArchivePO getById(String id) {
        return alertRuleArchiveMapper.selectById(id);
    }

    /**
     * 根据规则编码查询归档记录
     *
     * @param ruleCode 规则编码
     * @return 归档记录列表
     */
    public List<AlertRuleArchivePO> listByRuleCode(String ruleCode) {
        LambdaQueryWrapper<AlertRuleArchivePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlertRuleArchivePO::getRuleCode, ruleCode)
                .eq(AlertRuleArchivePO::getDelFlag, "0")
                .orderByDesc(AlertRuleArchivePO::getCreateTime);
        return alertRuleArchiveMapper.selectList(queryWrapper);
    }

    /**
     * 根据规则编码和版本号查询归档记录
     *
     * @param ruleCode 规则编码
     * @param archiveVersion 归档版本号
     * @return 归档记录
     */
    public AlertRuleArchivePO getByRuleCodeAndVersion(String ruleCode, String archiveVersion) {
        LambdaQueryWrapper<AlertRuleArchivePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AlertRuleArchivePO::getRuleCode, ruleCode)
                .eq(AlertRuleArchivePO::getArchiveVersion, archiveVersion)
                .eq(AlertRuleArchivePO::getDelFlag, "0");
        return alertRuleArchiveMapper.selectOne(queryWrapper);
    }

    /**
     * 分页查询归档记录
     *
     * @param page 分页参数
     * @param ruleCode 规则编码（可选）
     * @param archiveVersion 归档版本号（可选）
     * @return 分页结果
     */
    public Page<AlertRuleArchivePO> page(Page<AlertRuleArchivePO> page, String ruleCode, String archiveVersion) {
        LambdaQueryWrapper<AlertRuleArchivePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(ruleCode != null, AlertRuleArchivePO::getRuleCode, ruleCode)
                .like(archiveVersion != null, AlertRuleArchivePO::getArchiveVersion, archiveVersion)
                .eq(AlertRuleArchivePO::getDelFlag, "0")
                .orderByDesc(AlertRuleArchivePO::getCreateTime);
        return alertRuleArchiveMapper.selectPage(page, queryWrapper);
    }

    /**
     * 保存归档记录
     *
     * @param archive 归档记录
     * @return 是否成功
     */
    public boolean save(AlertRuleArchivePO archive) {
        return alertRuleArchiveMapper.insert(archive) > 0;
    }

    /**
     * 更新归档记录
     *
     * @param archive 归档记录
     * @return 是否成功
     */
    public boolean updateById(AlertRuleArchivePO archive) {
        return alertRuleArchiveMapper.updateById(archive) > 0;
    }
}
