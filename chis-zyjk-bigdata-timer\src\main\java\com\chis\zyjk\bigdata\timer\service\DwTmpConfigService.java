package com.chis.zyjk.bigdata.timer.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chis.project.frame.common.tools.core.util.StrUtil;
import com.chis.zyjk.bigdata.common.enums.CleanModelEnum;
import com.chis.zyjk.bigdata.common.enums.ExecuteModeEnum;
import com.chis.zyjk.bigdata.common.enums.ExecuteStatusEnum;
import com.chis.zyjk.bigdata.common.pojo.dto.dataclean.DataCleaningDTO;
import com.chis.zyjk.bigdata.common.pojo.dto.system.ConfigKeyDTO;
import com.chis.zyjk.bigdata.common.pojo.po.DwTmpConfigPO;
import com.chis.zyjk.bigdata.common.pojo.vo.dataclean.DataCleaningVO;
import com.chis.zyjk.bigdata.common.remote.SysConfigRemote;
import com.chis.zyjk.bigdata.timer.jobhandler.result.DwCleaningJobResult;
import com.chis.zyjk.bigdata.timer.mapper.DwTmpConfigMapper;
import com.chis.zyjk.bigdata.timer.remote.IDataCleaningSqlService;
import com.chis.zyjk.core.common.handler.FeignResponseHandler;
import com.chis.zyjk.datasync.timer.remote.FeignClientConfig;
import com.way.common.core.domain.R;
import com.way.common.core.exception.ServiceException;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DwTmpConfigService extends ServiceImpl<DwTmpConfigMapper, DwTmpConfigPO> {

    private final ThreadPoolExecutor dataCleaningThreadPool;
    private final SysConfigRemote sysConfigRemote;
    private final DwTmpConfigMapper dwTmpConfigMapper;

    public DwCleaningJobResult handlerTmpConfig(String fullJobName, DwCleaningJobResult dwCleaningJobResult) {
        log.info("开始处理清洗分层任务，任务名称: {}", fullJobName);
        XxlJobHelper.log("开始处理清洗分层任务!");
        // 修改状态，执行开始时间和执行结束时间为空
        LambdaUpdateWrapper<DwTmpConfigPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(DwTmpConfigPO::getExecuteState, ExecuteStatusEnum.PERFORMED.getCode())
                .set(DwTmpConfigPO::getStartTime, null)
                .set(DwTmpConfigPO::getEndTime, null)
                .eq(DwTmpConfigPO::getIfEnable, 1)
                .eq(DwTmpConfigPO::getCleanMode, CleanModelEnum.BATCH.getCode());
        dwTmpConfigMapper.update(null, updateWrapper);

        // a. 从库中获取清洗服务参数：根据执行顺序排序返回，查询状态为启用、清洗模式为批处理的所有数据
        List<DwTmpConfigPO> cleaningTasks = getEnabledBatchCleaningTasks();

        if (cleaningTasks.isEmpty()) {
            log.info("没有找到启用的批处理清洗任务");
            XxlJobHelper.log("没有找到启用的批处理清洗任务!");
            dwCleaningJobResult.setResult("没有找到启用的批处理清洗任务");
            return dwCleaningJobResult;
        }

        // b. 按照顺序调用清洗服务接口
        executeCleaningTasksByOrder(cleaningTasks);

        // d. 所有任务结束，更新主体任务的状态
        dwCleaningJobResult.setResult("所有清洗任务执行完成");
        log.info("所有清洗任务执行完成");
        XxlJobHelper.log("所有分层清洗任务执行完成!");

        return dwCleaningJobResult;
    }

    /**
     * 获取启用的批处理清洗任务，按执行顺序排序
     */
    private List<DwTmpConfigPO> getEnabledBatchCleaningTasks() {
        LambdaQueryWrapper<DwTmpConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DwTmpConfigPO::getIfEnable, 1).eq(DwTmpConfigPO::getCleanMode, CleanModelEnum.BATCH.getCode()).orderByAsc(DwTmpConfigPO::getExecuteNum);
        return list(queryWrapper);
    }

    /**
     * 按照顺序执行清洗任务
     */
    private void executeCleaningTasksByOrder(List<DwTmpConfigPO> cleaningTasks) {
        // 按执行顺序分组
        Map<Integer, List<DwTmpConfigPO>> tasksByOrderMap = cleaningTasks.stream().collect(Collectors.groupingBy(DwTmpConfigPO::getExecuteNum));

        // 按顺序执行每组任务
        List<Integer> sortedOrders = tasksByOrderMap.keySet().stream().sorted().collect(Collectors.toList());

        for (Integer order : sortedOrders) {
            List<DwTmpConfigPO> tasksInOrder = tasksByOrderMap.get(order);
            log.info("开始执行顺序 {} 的任务，共 {} 个任务", order, tasksInOrder.size());
            XxlJobHelper.log("开始执行顺序 {} 的任务，共 {} 个任务", order, tasksInOrder.size());
            // 相同顺序的任务并行执行
            executeTasksInParallel(tasksInOrder);

            log.info("顺序 {} 的任务执行完成", order);
        }
    }

    /**
     * 并行执行相同顺序的任务
     */
    private void executeTasksInParallel(List<DwTmpConfigPO> tasks) {
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (DwTmpConfigPO task : tasks) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                // 执行任务
                executeCleaningTask(task);
            }, dataCleaningThreadPool);

            futures.add(future);
        }

        // 等待所有任务完成
        CompletableFuture<Void> allTasks = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

        try {
            // 阻塞等待所有任务完成
            allTasks.get();
        } catch (InterruptedException | ExecutionException e) {
            log.error("等待任务完成时发生异常", e);
            throw new RuntimeException("任务执行失败", e);
        }
    }

    /**
     * 执行单个清洗任务
     */
    private void executeCleaningTask(DwTmpConfigPO task) {
        log.info("开始执行清洗任务表名: {}", task.getTableName());
        XxlJobHelper.log("开始执行清洗任务表名: {}", task.getTableName());
        try {
            // 更新任务开始时间
            task.setStartTime(LocalDateTime.now());
            task.setExecuteState(ExecuteStatusEnum.GOING.getCode());
            this.updateById(task);

            if (ExecuteModeEnum.SQL.getCode().equals(task.getExecuteMode())) {
                // SQL 执行模式
                executeSqlTask(task);
            } else if (ExecuteModeEnum.API.getCode().equals(task.getExecuteMode())) {
                // API 执行模式，先不写
                executeApiTask(task);
            } else {
                log.warn("未知的执行模式: {}", task.getExecuteMode());
                throw new RuntimeException("未知的执行模式: " + task.getExecuteMode());
            }

            // 更新任务结束时间和状态
            task.setEndTime(LocalDateTime.now());
            task.setExecuteState(ExecuteStatusEnum.SUCCESS.getCode());
            this.updateById(task);

            log.info("清洗任务执行成功: {}", task.getTableName());

        } catch (Exception e) {
            log.error("清洗任务执行失败: {}, 错误: {}", task.getTableName(), e.getMessage(), e);

            // 更新任务失败状态
            task.setEndTime(LocalDateTime.now());
            task.setExecuteState(ExecuteStatusEnum.FAIL.getCode());
            this.updateById(task);

            throw new RuntimeException("清洗任务执行失败: " + task.getTableName(), e);
        }
    }

    /**
     * 执行 SQL 清洗任务
     */
    private void executeSqlTask(DwTmpConfigPO task) {
        DataCleaningDTO dataCleaningDTO = new DataCleaningDTO();
        dataCleaningDTO.setTableName(task.getTableName());
        dataCleaningDTO.setEncodeFlag(true);
        dataCleaningDTO.setExecuteParam(task.getExecuteParam());

        // 对 SQL 语句进行 URL 编码，避免特殊字符（如 >=）被误解析为参数
        String executeSql = task.getExecuteSql();
        if (StrUtil.isNotBlank(executeSql)) {
            try {
                // URL 编码 SQL 语句
                String encodedSql = URLEncoder.encode(executeSql, "UTF-8");
                dataCleaningDTO.setExecuteSql(encodedSql);
            } catch (UnsupportedEncodingException e) {
                log.warn("SQL 语句编码失败，使用原始 SQL: {}", e.getMessage());
                throw new ServiceException("SQL 语句编码失败");
            }
        }

        String remoteUrl = "http://localhost:19003/bigdata-api-dataserver/dwCleaning/dataCleaning";
        //String remoteUrl = getZyjkRequestUrl() + getSqlBigDataCleaningUrl();
        log.info("执行sql类型的remoteUrl：{}", remoteUrl);
        try (FeignClientConfig.CloseableFeignClient<IDataCleaningSqlService> closeableClient = createCloseableClient(IDataCleaningSqlService.class, remoteUrl)) {

            R<DataCleaningVO> remoteData = closeableClient.getClient().execute(dataCleaningDTO);
            if (remoteData == null || remoteData.getCode() != 200) {
                String msg = "sql清洗数据调用失败，失败原因：" + (remoteData == null ? "接口异常" : remoteData.getMsg());
                throw new ServiceException(msg);
            }
            task.setExecuteParam(remoteData.getData().getReturnParam());
            log.info("SQL 清洗任务执行成功: {}", task.getTableName());
        }
    }

    /**
     * 执行 API 清洗任务
     */
    private void executeApiTask(DwTmpConfigPO task) {
        log.warn("API 执行模式暂未实现");
    }

    private String getZyjkRequestUrl() {
        try {
            return FeignResponseHandler.extractRequired(sysConfigRemote.getConfigKey(new ConfigKeyDTO("zyjk.request.url")), "系统参数");
        } catch (Exception e) {
            throw new ServiceException("获取职业健康2.0url失败");
        }
    }

    private String getSqlBigDataCleaningUrl() {
        try {
            return FeignResponseHandler.extractRequired(sysConfigRemote.getConfigKey(new ConfigKeyDTO("bigdata.sql.url")), "系统参数");
        } catch (Exception e) {
            throw new ServiceException("sql清洗数据url失败");
        }
    }

    private <T> FeignClientConfig.CloseableFeignClient<T> createCloseableClient(Class<T> serviceClass, String remoteUrl) {
        return new FeignClientConfig.EnhancedFeignClientFactory<T>()
                .withTimeouts(10000, 60000)  // 设置超时时间
                .withLogLevel(feign.Logger.Level.BASIC)  // 设置日志级别
                .createCloseableClient(serviceClass, remoteUrl);
    }
}
