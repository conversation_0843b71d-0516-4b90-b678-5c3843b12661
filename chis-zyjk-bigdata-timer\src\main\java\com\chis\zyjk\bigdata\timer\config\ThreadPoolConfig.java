package com.chis.zyjk.bigdata.timer.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * 线程池配置
 * 优化说明：
 * 1. 核心线程数设置为 CPU 核心数的 2 倍，适合 I/O 密集型任务
 * 2. 最大线程数设置为核心线程数的 2 倍，避免过多线程竞争
 * 3. 使用 LinkedBlockingQueue 避免任务丢失
 * 4. 增加自定义线程工厂，便于问题排查
 * 5. 使用 AbortPolicy 拒绝策略，及时发现系统瓶颈
 */
@Slf4j
@Configuration
public class ThreadPoolConfig {

	/**
	 * 数据清洗任务线程池
	 * 专门用于数据清洗任务的并行执行
	 */
	@Bean("dataCleaningThreadPool")
	public ThreadPoolExecutor dataCleaningThreadPool() {
		// 获取 CPU 核心数
		int corePoolSize = Runtime.getRuntime().availableProcessors() * 2;
		int maximumPoolSize = corePoolSize * 2;
		long keepAliveTime = 60L;
		TimeUnit unit = TimeUnit.SECONDS;

		// 使用 LinkedBlockingQueue，容量设置为最大线程数的 10 倍
		BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(maximumPoolSize * 10);

		// 自定义线程工厂
		ThreadFactory threadFactory = new ThreadFactory() {
			private int threadNumber = 1;

			@Override
			public Thread newThread(Runnable r) {
				Thread thread = new Thread(r, "data-cleaning-thread-" + threadNumber++);
				thread.setDaemon(false);
				thread.setPriority(Thread.NORM_PRIORITY);
				return thread;
			}
		};

		// 拒绝策略：当线程池和队列都满时，抛出异常
		RejectedExecutionHandler handler = new ThreadPoolExecutor.AbortPolicy();

		ThreadPoolExecutor executor = new ThreadPoolExecutor(
				corePoolSize,
				maximumPoolSize,
				keepAliveTime,
				unit,
				workQueue,
				threadFactory,
				handler);

		// 允许核心线程超时
		executor.allowCoreThreadTimeOut(true);

		log.info("数据清洗线程池初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}", corePoolSize, maximumPoolSize, workQueue.remainingCapacity());

		return executor;
	}


}
