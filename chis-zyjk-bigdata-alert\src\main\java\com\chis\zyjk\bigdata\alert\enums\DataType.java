package com.chis.zyjk.bigdata.alert.enums;

import com.chis.zyjk.core.common.enums.base.StringCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据类型
 */
@Getter
@AllArgsConstructor
public enum DataType implements StringCodeEnum {
    /**
     * 字符串表达式
     */
    STRING("STRING", "字符串表达式"),
    /**
     * 数据路径
     */
    PATH("PATH", "数据路径"),
    /**
     * JSON对象
     */
    JSON("JSON", "JSON对象");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    @Override
    public String getEnumDesc() {
        return "数据类型";
    }
}
