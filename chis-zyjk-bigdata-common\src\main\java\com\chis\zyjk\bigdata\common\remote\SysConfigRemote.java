package com.chis.zyjk.bigdata.common.remote;


import com.chis.zyjk.bigdata.common.pojo.dto.system.ConfigKeyDTO;
import com.way.common.core.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "chis-way-system", contextId = "sysConfig")
public interface SysConfigRemote {

    /**
     * 根据配置键获取配置值
     *
     * @param configKeyDTO
     * @return 参数值
     */
    @PostMapping(value = "/config/byConfigKey")
    R<String> getConfigKey(@RequestBody @Validated ConfigKeyDTO configKeyDTO);
}
