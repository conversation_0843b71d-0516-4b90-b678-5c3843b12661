package com.chis.zyjk.bigdata.alert.liteflow.pojo.context;

import com.yomahub.liteflow.slot.DefaultContext;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 全局上下文
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GlobalContext extends DefaultContext {
    /**
     * 规则编码
     */
    private String ruleCode;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 规则表达式
     */
    private String expression;

    public String toString() {
        return "[" + ruleCode + ": " + ruleName + "]";
    }
}
