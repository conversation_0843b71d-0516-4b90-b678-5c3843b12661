package com.chis.zyjk.bigdata.common.enums;

import com.chis.zyjk.core.common.enums.base.NumericCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 执行转态枚举
 */
@Getter
@AllArgsConstructor
public enum ExecuteModeEnum implements NumericCodeEnum {
    SQL(1, "sql"),
    API(2, "api");

    private final Integer code;
    private final String desc;

    @Override
    public String getEnumDesc() {
        return "执行方式";
    }
}
